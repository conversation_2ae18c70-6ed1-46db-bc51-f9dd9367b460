<?php
// Start output buffering at the beginning
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Ensure user has appropriate role
requireLogin();

// Only relevant roles can access this page
if (!hasRole('GodMode', 'Superadmin', 'Logistics', 'Department', 'HealthCenter', 'HIMU')) {
    header('Location: /choims/index.php');
    exit();
}

// Check if the form was submitted
if ($_SERVER['REQUEST_METHOD'] != 'POST') {
    header('Location: /choims/modules/transfers/batch/select.php');
    exit();
}

// Get the item type
$itemType = sanitizeInput($_POST['item_type']);

// Check if items were selected
$selectedAssets = isset($_POST['selected_assets']) ? $_POST['selected_assets'] : [];
$selectedInventory = isset($_POST['selected_inventory']) ? $_POST['selected_inventory'] : [];
$transferQuantities = isset($_POST['transfer_quantity']) ? $_POST['transfer_quantity'] : [];

// Validate that at least one item was selected
if (empty($selectedAssets) && empty($selectedInventory)) {
    // Determine which tab to redirect to based on the original item type or default to asset
    $redirectType = ($itemType === 'mixed') ? 'asset' : $itemType;
    header('Location: /choims/modules/transfers/batch/select.php?type=' . $redirectType . '&error=no_selection');
    exit();
}

// Get locations for dropdowns
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locations = [];
while ($location = mysqli_fetch_assoc($locationsResult)) {
    $locations[$location['location_id']] = $location;
}

// Get user's location for default source
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Initialize arrays to store item details
$assets = [];
$inventory = [];
$sourceLocations = [];

// If assets are selected, fetch their details
if (!empty($selectedAssets)) {
    $assetIds = implode(',', array_map(function($id) { return (int)$id; }, $selectedAssets));
    $assetsQuery = "SELECT a.asset_id, a.asset_name, a.serial_number, a.current_location_id,
                      s.sku_code, s.sku_name, c.category_name, l.location_name
                    FROM fixed_assets a
                    JOIN sku_master s ON a.sku_id = s.sku_id
                    JOIN categories c ON s.category_id = c.category_id
                    JOIN locations l ON a.current_location_id = l.location_id
                    WHERE a.asset_id IN ($assetIds)";
    $assetsResult = mysqli_query($conn, $assetsQuery);

    while ($asset = mysqli_fetch_assoc($assetsResult)) {
        $assets[$asset['asset_id']] = $asset;
        // Track source locations
        $sourceLocations[$asset['current_location_id']] = $asset['location_name'];
    }
}

// If inventory items are selected, fetch their details
if (!empty($selectedInventory)) {
    $inventoryIds = implode(',', array_map(function($id) { return (int)$id; }, $selectedInventory));
    $inventoryQuery = "SELECT i.inventory_id, i.current_quantity, i.location_id,
                         s.sku_code, s.sku_name, s.unit_of_measure, c.category_name, l.location_name
                        FROM consumable_inventory i
                        JOIN sku_master s ON i.sku_id = s.sku_id
                        JOIN categories c ON s.category_id = c.category_id
                        JOIN locations l ON i.location_id = l.location_id
                        WHERE i.inventory_id IN ($inventoryIds)";
    $inventoryResult = mysqli_query($conn, $inventoryQuery);

    while ($item = mysqli_fetch_assoc($inventoryResult)) {
        $inventory[$item['inventory_id']] = $item;
        // Track source locations
        $sourceLocations[$item['location_id']] = $item['location_name'];
    }
}

// Handle form submission for initiating transfers
if (isset($_POST['submit_transfers'])) {
    $destination_location_id = sanitizeInput($_POST['destination_location_id']);
    $source_location_id = sanitizeInput($_POST['source_location_id']);
    $transfer_notes = sanitizeInput($_POST['transfer_notes']);

    // Check if any fixed assets are missing required MR receipt type
    if (!empty($selectedAssets)) {
        // Build a query to check if any assets are missing receipt type
        $missingMRQuery = "
            SELECT fa.asset_id, fa.asset_name
            FROM fixed_assets fa
            WHERE fa.asset_id IN (" . implode(',', array_map('intval', $selectedAssets)) . ")
            AND (fa.receipt_type IS NULL OR fa.receipt_type = '')
        ";

        $missingMRResult = mysqli_query($conn, $missingMRQuery);

        if (mysqli_num_rows($missingMRResult) > 0) {
            // Some assets are missing MR receipt type
            $missingAssets = [];
            while ($asset = mysqli_fetch_assoc($missingMRResult)) {
                $missingAssets[] = $asset['asset_name'] . " (ID: " . $asset['asset_id'] . ")";
            }

            $error_message = "The following assets are missing required MR receipt information: ";
            $error_message .= implode(", ", $missingAssets);
            $error_message .= ". Please update these assets with the required information before transferring.";

            $_SESSION['batch_transfer_error'] = $error_message;
            $redirectType = ($itemType === 'mixed') ? 'asset' : $itemType;
            header("Location: /choims/modules/transfers/batch/select.php?type=" . $redirectType . "&error=" . urlencode($error_message));
            ob_end_flush();
            exit();
        }
    }

    // Start a transaction
    mysqli_begin_transaction($conn);

    try {
        // Call stored procedure to initiate batch transfer
        $procedure = "CALL initiate_batch_transfer(?, ?, ?, ?, @batch_id)";
        $stmt = mysqli_prepare($conn, $procedure);
        mysqli_stmt_bind_param($stmt, 'iiss', $source_location_id, $destination_location_id, $_SESSION['user_id'], $transfer_notes);

        if (mysqli_stmt_execute($stmt)) {
            mysqli_stmt_close($stmt);

            // Get the batch ID from the procedure
            $result = mysqli_query($conn, "SELECT @batch_id AS batch_id");
            $row = mysqli_fetch_assoc($result);
            $batch_id = $row['batch_id'];
            mysqli_free_result($result);

            // Process asset transfers
            if (!empty($selectedAssets)) {
                $assetInsertQuery = "INSERT INTO batch_transfer_assets (batch_id, asset_id) VALUES (?, ?)";
                $assetStmt = mysqli_prepare($conn, $assetInsertQuery);

                $statusChangedAssets = [];

                foreach ($selectedAssets as $asset_id) {
                    // Check if the asset is currently "In use"
                    $checkStatusQuery = "SELECT status FROM fixed_assets WHERE asset_id = ?";
                    $checkStatusStmt = mysqli_prepare($conn, $checkStatusQuery);
                    mysqli_stmt_bind_param($checkStatusStmt, 'i', $asset_id);
                    mysqli_stmt_execute($checkStatusStmt);
                    $statusResult = mysqli_stmt_get_result($checkStatusStmt);
                    $assetStatus = mysqli_fetch_assoc($statusResult);
                    mysqli_stmt_close($checkStatusStmt);

                    // If the asset is "In use", update it to "Available"
                    if ($assetStatus && $assetStatus['status'] === 'In use') {
                        $updateStatusQuery = "UPDATE fixed_assets SET status = 'Available', updated_at = NOW() WHERE asset_id = ?";
                        $updateStatusStmt = mysqli_prepare($conn, $updateStatusQuery);
                        mysqli_stmt_bind_param($updateStatusStmt, 'i', $asset_id);
                        mysqli_stmt_execute($updateStatusStmt);
                        mysqli_stmt_close($updateStatusStmt);

                        // Keep track of changed assets to notify user
                        $statusChangedAssets[] = $asset_id;

                        // Log the status change
                        createAuditLog(
                            $_SESSION['user_id'],
                            'update',
                            'fixed_assets',
                            $asset_id,
                            json_encode(['status' => 'In use']),
                            json_encode(['status' => 'Available'])
                        );
                    }

                    // Add the asset to the batch transfer
                    mysqli_stmt_bind_param($assetStmt, 'ii', $batch_id, $asset_id);
                    mysqli_stmt_execute($assetStmt);
                }

                mysqli_stmt_close($assetStmt);

                // Store the count of changed assets in session for notification
                if (!empty($statusChangedAssets)) {
                    $_SESSION['status_changed_assets'] = count($statusChangedAssets);
                }
            }

            // Process inventory transfers
            if (!empty($selectedInventory)) {
                $inventoryInsertQuery = "INSERT INTO batch_transfer_inventory (batch_id, inventory_id, quantity) VALUES (?, ?, ?)";
                $inventoryStmt = mysqli_prepare($conn, $inventoryInsertQuery);

                foreach ($selectedInventory as $inventory_id) {
                    $quantity = isset($transferQuantities[$inventory_id]) ? sanitizeInput($transferQuantities[$inventory_id]) : 1;
                    mysqli_stmt_bind_param($inventoryStmt, 'iii', $batch_id, $inventory_id, $quantity);
                    mysqli_stmt_execute($inventoryStmt);
                }

                mysqli_stmt_close($inventoryStmt);
            }

            // Create audit log
            $auditSql = "INSERT INTO audit_logs (user_id, action, entity_type, entity_id, old_values, new_values, ip_address)
                      VALUES (?, ?, ?, ?, NULL, ?, ?)";
            $auditStmt = mysqli_prepare($conn, $auditSql);

            $user_id = $_SESSION['user_id'];
            $action = 'Create';
            $entity_type = 'Batch Transfer';
            $entity_id = $batch_id;
            $ip_address = $_SERVER['REMOTE_ADDR'];
            $new_values = json_encode([
                'source_location_id' => $source_location_id,
                'destination_location_id' => $destination_location_id,
                'asset_count' => count($selectedAssets),
                'inventory_count' => count($selectedInventory)
            ]);

            mysqli_stmt_bind_param($auditStmt, 'ississ', $user_id, $action, $entity_type, $entity_id, $new_values, $ip_address);
            mysqli_stmt_execute($auditStmt);
            mysqli_stmt_close($auditStmt);

            // Create notification for superadmins about new batch transfer
            createAuditNotification(
                $conn,
                $user_id,
                $action,
                $entity_type,
                $entity_id,
                $new_values
            );

            // Check if any items require HIMU approval
            $requiresHIMU = false;

            // Check fixed assets for HIMU approval requirement
            if (!empty($selectedAssets)) {
                $assetCheckQuery = "
                    SELECT COUNT(*) as himu_count
                    FROM fixed_assets fa
                    JOIN sku_master sm ON fa.sku_id = sm.sku_id
                    JOIN categories c ON sm.category_id = c.category_id
                    WHERE fa.asset_id IN (" . implode(',', array_map('intval', $selectedAssets)) . ")
                    AND c.requires_himu_approval = 1";

                $assetHimuResult = mysqli_query($conn, $assetCheckQuery);
                $assetHimuCount = mysqli_fetch_assoc($assetHimuResult);

                if ($assetHimuCount['himu_count'] > 0) {
                    $requiresHIMU = true;
                }
            }

            // Check inventory items for HIMU approval requirement
            if (!empty($selectedInventory) && !$requiresHIMU) {
                $inventoryCheckQuery = "
                    SELECT COUNT(*) as himu_count
                    FROM consumable_inventory ci
                    JOIN sku_master sm ON ci.sku_id = sm.sku_id
                    JOIN categories c ON sm.category_id = c.category_id
                    WHERE ci.inventory_id IN (" . implode(',', array_map('intval', $selectedInventory)) . ")
                    AND c.requires_himu_approval = 1";

                $inventoryHimuResult = mysqli_query($conn, $inventoryCheckQuery);
                $inventoryHimuCount = mysqli_fetch_assoc($inventoryHimuResult);

                if ($inventoryHimuCount['himu_count'] > 0) {
                    $requiresHIMU = true;
                }
            }

            // Update the batch transfer record with HIMU approval flag
            $updateHimuQuery = "UPDATE batch_transfers SET requires_himu_approval = ? WHERE batch_id = ?";
            $updateHimuStmt = mysqli_prepare($conn, $updateHimuQuery);
            mysqli_stmt_bind_param($updateHimuStmt, 'ii', $requiresHIMU, $batch_id);
            mysqli_stmt_execute($updateHimuStmt);
            mysqli_stmt_close($updateHimuStmt);

            // Commit the transaction
            mysqli_commit($conn);

            // Redirect with success message
            $message = "Batch transfer #" . $batch_id . " initiated successfully with " .
                      (count($selectedAssets) + count($selectedInventory)) . " items";

            // If this includes consumable inventory, add show_ptr parameter
            if (($itemType == 'inventory' || $itemType == 'mixed') && !empty($selectedInventory)) {
                header("Location: /choims/modules/transfers/batch/view.php?id=" . $batch_id . "&success=" . urlencode($message) . "&show_ptr=1");
            } else {
                header("Location: /choims/modules/transfers/batch/view.php?id=" . $batch_id . "&success=" . urlencode($message));
            }
            ob_end_flush();
            exit();
        } else {
            // Rollback on error
            mysqli_rollback($conn);
            $error_message = "Failed to initiate batch transfer: " . mysqli_error($conn);
            $redirectType = ($itemType === 'mixed') ? 'asset' : $itemType;
            header("Location: /choims/modules/transfers/batch/select.php?type=" . $redirectType . "&error=" . urlencode($error_message));
            ob_end_flush();
            exit();
        }
    } catch (Exception $e) {
        // Rollback on exception
        mysqli_rollback($conn);
        $error_message = "An error occurred: " . $e->getMessage();
        $redirectType = ($itemType === 'mixed') ? 'asset' : $itemType;
        header("Location: /choims/modules/transfers/batch/select.php?type=" . $redirectType . "&error=" . urlencode($error_message));
        ob_end_flush();
        exit();
    }
}
?>

<div class="container-fluid">
    <div class="modern-page-header mb-4">
        <div class="d-sm-flex align-items-center justify-content-between">
            <div>
                <h1 class="h3 mb-0">Batch Transfer</h1>
                <p class="text-muted mb-0">Review and confirm your transfer details</p>
            </div>
            <a href="/choims/modules/transfers/batch/select.php?type=<?php echo ($itemType === 'mixed') ? 'asset' : $itemType; ?>" class="btn btn-soft-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Selection
            </a>
        </div>

        <!-- Progress Steps -->
        <div class="progress-steps mt-4">
            <div class="step completed">
                <div class="step-icon"><i class="fas fa-list"></i></div>
                <div class="step-label">Select Items</div>
            </div>
            <div class="step-connector completed"></div>
            <div class="step active">
                <div class="step-icon"><i class="fas fa-check"></i></div>
                <div class="step-label">Confirm Details</div>
            </div>
            <div class="step-connector"></div>
            <div class="step">
                <div class="step-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="step-label">Complete Transfer</div>
            </div>
        </div>
    </div>

    <?php if (!empty($assets) || !empty($inventory)): ?>
    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-boxes"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Selected Items</h6>
            </div>
            <div class="badge bg-soft-primary"><?php echo count($assets) + count($inventory); ?> items</div>
        </div>
        <div class="modern-card-body">
            <!-- Fixed Assets Section -->
            <?php if (!empty($assets)): ?>
            <div class="section-header">
                <h5 class="mb-3">Fixed Assets</h5>
                <div class="badge bg-soft-success"><?php echo count($assets); ?> items</div>
            </div>
            <div class="table-responsive mb-4">
                <table class="modern-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Asset ID</th>
                            <th>SKU Code</th>
                            <th>Item Name</th>
                            <th>Serial Number</th>
                            <th>Category</th>
                            <th>Current Location</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($assets as $asset): ?>
                            <tr class="item-row">
                                <td><span class="id-badge"><?php echo $asset['asset_id']; ?></span></td>
                                <td><span class="code-badge"><?php echo $asset['sku_code']; ?></span></td>
                                <td>
                                    <div class="item-name"><?php echo $asset['asset_name']; ?></div>
                                </td>
                                <td><?php echo $asset['serial_number'] ?: '<span class="text-muted">—</span>'; ?></td>
                                <td><span class="category-badge"><?php echo $asset['category_name']; ?></span></td>
                                <td>
                                    <div class="location-badge">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo $asset['location_name']; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>

            <!-- Inventory Section -->
            <?php if (!empty($inventory)): ?>
            <div class="section-header">
                <h5 class="mb-3">Consumable Inventory</h5>
                <div class="badge bg-soft-info"><?php echo count($inventory); ?> items</div>
            </div>
            <div class="table-responsive">
                <table class="modern-table" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Inventory ID</th>
                            <th>SKU Code</th>
                            <th>Item Name</th>
                            <th>Category</th>
                            <th>Current Location</th>
                            <th>Quantity to Transfer</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($inventory as $id => $item): ?>
                            <tr class="item-row">
                                <td><span class="id-badge"><?php echo $item['inventory_id']; ?></span></td>
                                <td><span class="code-badge"><?php echo $item['sku_code']; ?></span></td>
                                <td>
                                    <div class="item-name"><?php echo $item['sku_name']; ?></div>
                                </td>
                                <td><span class="category-badge"><?php echo $item['category_name']; ?></span></td>
                                <td>
                                    <div class="location-badge">
                                        <i class="fas fa-map-marker-alt me-1"></i>
                                        <?php echo $item['location_name']; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php
                                    $qty = isset($transferQuantities[$id]) ? $transferQuantities[$id] : 1;
                                    ?>
                                    <div class="quantity-info">
                                        <span class="quantity-badge"><?php echo $qty; ?> <?php echo $item['unit_of_measure']; ?></span>
                                        <span class="text-muted small">of <?php echo $item['current_quantity']; ?> <?php echo $item['unit_of_measure']; ?> available</span>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="modern-card mb-4">
        <div class="modern-card-header">
            <div class="d-flex align-items-center">
                <div class="card-icon-bg">
                    <i class="fas fa-exchange-alt"></i>
                </div>
                <h6 class="m-0 ms-2 fw-bold">Transfer Details</h6>
            </div>
        </div>
        <div class="modern-card-body">
            <form method="post" action="">
                <!-- Preserve the selections from the previous form -->
                <input type="hidden" name="item_type" value="<?php echo $itemType; ?>">
                <?php foreach ($selectedAssets as $asset_id): ?>
                    <input type="hidden" name="selected_assets[]" value="<?php echo $asset_id; ?>">
                <?php endforeach; ?>

                <?php foreach ($selectedInventory as $inventory_id): ?>
                    <input type="hidden" name="selected_inventory[]" value="<?php echo $inventory_id; ?>">
                    <input type="hidden" name="transfer_quantity[<?php echo $inventory_id; ?>]" value="<?php echo $transferQuantities[$inventory_id]; ?>">
                <?php endforeach; ?>

                <div class="transfer-form-container">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <div class="form-card mb-3">
                                <div class="form-card-header">
                                    <i class="fas fa-arrow-right me-2 text-success"></i>
                                    <label for="source_location_id" class="form-label mb-0">Source Location</label>
                                    <span class="required-indicator">*</span>
                                </div>
                                <div class="form-card-body">
                                    <?php if (hasRole('himu') && $userLocationId): ?>
                                        <input type="hidden" id="source_location_id" name="source_location_id" value="<?php echo $userLocationId; ?>">
                                        <div class="location-display">
                                            <i class="fas fa-lock me-2 text-muted"></i>
                                            <span><?php echo isset($locations[$userLocationId]) ? $locations[$userLocationId]['location_name'] : 'Your Location'; ?></span>
                                        </div>
                                        <div class="form-info">
                                            <i class="fas fa-info-circle me-1"></i> Source location is locked to your assigned location.
                                        </div>
                                    <?php else: ?>
                                        <select class="modern-select" id="source_location_id" name="source_location_id" required>
                                            <?php
                                            // If all items are from the same location, pre-select it
                                            $singleSourceId = count($sourceLocations) == 1 ? key($sourceLocations) : null;

                                            foreach ($sourceLocations as $id => $name):
                                            ?>
                                                <option value="<?php echo $id; ?>" <?php echo ($singleSourceId == $id) ? 'selected' : ''; ?>>
                                                    <?php echo $name; ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <?php if (count($sourceLocations) > 1): ?>
                                            <div class="form-alert">
                                                <i class="fas fa-exclamation-triangle me-1"></i>
                                                You've selected items from multiple locations. Please select the specific source location.
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="form-card mb-3">
                                <div class="form-card-header">
                                    <i class="fas fa-arrow-left me-2 text-primary"></i>
                                    <label for="destination_location_id" class="form-label mb-0">Destination Location</label>
                                    <span class="required-indicator">*</span>
                                </div>
                                <div class="form-card-body">
                                    <?php
                                    // Check if user is department, health center, or HIMU role
                                    $isRestrictedUser = hasRole('Department', 'HealthCenter', 'HIMU');

                                    // Define the ID for Property and Supply/Logistic Management Unit location
                                    $logisticsLocationId = 5; // Based on the database value

                                    if ($isRestrictedUser):
                                    ?>
                                        <input type="hidden" id="destination_location_id" name="destination_location_id" value="<?php echo $logisticsLocationId; ?>">
                                        <div class="location-display">
                                            <i class="fas fa-lock me-2 text-muted"></i>
                                            <span>PROPERTY AND SUPPLY/LOGISTIC MANAGEMENT UNIT</span>
                                        </div>
                                        <div class="form-info">
                                            <i class="fas fa-info-circle me-1"></i> Destination is locked to Property and Supply/Logistic Management Unit.
                                        </div>
                                    <?php else: ?>
                                        <select class="modern-select" id="destination_location_id" name="destination_location_id" required>
                                            <option value="">Select Destination</option>
                                            <?php foreach ($locations as $id => $location): ?>
                                                <?php if (!array_key_exists($id, $sourceLocations)): ?>
                                                    <option value="<?php echo $id; ?>">
                                                        <?php echo $location['location_name']; ?>
                                                    </option>
                                                <?php endif; ?>
                                            <?php endforeach; ?>
                                        </select>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-12">
                        <div class="form-card mb-3">
                            <div class="form-card-header">
                                <i class="fas fa-sticky-note me-2 text-warning"></i>
                                <label for="transfer_notes" class="form-label mb-0">Transfer Notes</label>
                            </div>
                            <div class="form-card-body">
                                <textarea class="modern-textarea" id="transfer_notes" name="transfer_notes" rows="3" placeholder="Enter any additional notes or reasons for this transfer..."><?php echo isset($_POST['transfer_notes']) ? htmlspecialchars($_POST['transfer_notes']) : ''; ?></textarea>
                                <div class="form-info mt-2">
                                    <i class="fas fa-info-circle me-1"></i> These notes will be visible to approvers and recipients of the transfer.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="form-actions mt-4">
                    <button type="submit" name="submit_transfers" class="btn btn-soft-primary">
                        <i class="fas fa-exchange-alt me-2"></i> Initiate <?php echo count($assets) + count($inventory); ?> Transfers
                    </button>
                    <a href="/choims/modules/transfers/batch/select.php?type=<?php echo ($itemType === 'mixed') ? 'asset' : $itemType; ?>" class="btn btn-soft-danger ms-2">
                        <i class="fas fa-times me-2"></i> Cancel
                    </a>
                </div>
            </form>
        </div>
    </div>
    <?php else: ?>
        <div class="alert alert-danger">
            No items were selected. <a href="/choims/modules/transfers/batch/select.php?type=<?php echo ($itemType === 'mixed') ? 'asset' : $itemType; ?>">Go back</a> and select items to transfer.
        </div>
    <?php endif; ?>
</div>

<style>
/* Modern React-like UI Styles */
:root {
    --primary: #2E7D32;
    --primary-light: #4CAF50;
    --primary-dark: #1B5E20;
    --primary-bg: rgba(46, 125, 50, 0.08);
    --secondary: #607D8B;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --light: #f8f9fa;
    --dark: #1e293b;
    --white: #ffffff;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
}

/* Modern Page Header */
.modern-page-header {
    background-color: var(--white);
    border-radius: 16px;
    padding: 1.5rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

/* Progress Steps */
.progress-steps {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 1.5rem;
    padding: 0 1rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 1;
}

.step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--gray-200);
    color: var(--gray-500);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.step.active .step-icon {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 0 0 5px var(--primary-bg);
}

.step.completed .step-icon {
    background-color: var(--success);
    color: white;
}

.step-label {
    font-size: 0.85rem;
    font-weight: 500;
    color: var(--gray-500);
}

.step.active .step-label {
    color: var(--primary);
    font-weight: 600;
}

.step.completed .step-label {
    color: var(--success);
}

.step-connector {
    flex-grow: 1;
    height: 2px;
    background-color: var(--gray-200);
    margin: 0 0.5rem;
    position: relative;
    top: -20px;
    z-index: 0;
}

.step-connector.completed {
    background-color: var(--success);
}

/* Modern Cards */
.modern-card {
    background-color: var(--white);
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.modern-card-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-100);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-icon-bg {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    background-color: var(--primary-bg);
    color: var(--primary);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.modern-card-body {
    padding: 1.5rem;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

/* Modern Tables */
.modern-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin-bottom: 1.5rem;
}

.modern-table thead th {
    background-color: var(--gray-100);
    color: var(--gray-500);
    font-weight: 600;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.75rem 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.modern-table thead th:first-child {
    border-top-left-radius: 10px;
}

.modern-table thead th:last-child {
    border-top-right-radius: 10px;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: var(--gray-50);
}

.modern-table td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--gray-200);
    color: var(--gray-700);
    font-size: 0.9rem;
}

.item-row {
    transition: all 0.2s ease;
}

.item-row:hover {
    background-color: var(--gray-100);
}

/* Badges and Labels */
.id-badge {
    background-color: var(--gray-100);
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
}

.code-badge {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: monospace;
}

.category-badge {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
}

.location-badge {
    display: flex;
    align-items: center;
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    font-weight: 500;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.8rem;
    width: fit-content;
}

.quantity-badge {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

.item-name {
    font-weight: 500;
    color: var(--dark);
}

.quantity-info {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

/* Soft Badges */
.bg-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

/* Form Elements */
.transfer-form-container {
    margin-bottom: 1.5rem;
}

.form-card {
    background-color: var(--white);
    border-radius: 12px;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
}

.form-card:hover {
    border-color: var(--primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.form-card-header {
    background-color: var(--gray-50);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
}

.form-card-body {
    padding: 1rem;
}

.required-indicator {
    color: var(--danger);
    margin-left: 0.25rem;
}

.modern-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--dark);
    font-size: 0.9rem;
    transition: all 0.2s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236b7280'%3E%3Cpath d='M7 10l5 5 5-5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 1rem center;
    background-size: 1.5rem;
}

.modern-select:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-bg);
}

.modern-textarea {
    width: 100%;
    padding: 0.75rem 1rem;
    border-radius: 10px;
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--dark);
    font-size: 0.9rem;
    transition: all 0.2s ease;
    resize: vertical;
    min-height: 100px;
}

.modern-textarea:focus {
    outline: none;
    border-color: var(--primary);
    box-shadow: 0 0 0 3px var(--primary-bg);
}

.location-display {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    background-color: var(--gray-100);
    border-radius: 10px;
    color: var(--gray-700);
    font-weight: 500;
}

.form-info {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
}

.form-alert {
    margin-top: 0.5rem;
    font-size: 0.8rem;
    color: var(--warning);
    display: flex;
    align-items: center;
    background-color: rgba(245, 158, 11, 0.1);
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* Buttons */
.btn-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary:hover {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
    transform: translateY(-2px);
}

.btn-soft-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-secondary:hover {
    background-color: var(--gray-500);
    color: white;
    box-shadow: 0 4px 10px rgba(107, 114, 128, 0.2);
    transform: translateY(-2px);
}

.btn-soft-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 1.25rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-danger:hover {
    background-color: var(--danger);
    color: white;
    box-shadow: 0 4px 10px rgba(239, 68, 68, 0.2);
    transform: translateY(-2px);
}

.form-actions {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    margin-top: 2rem;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any needed JavaScript here

    // Prevent submitting to same destination as source
    document.querySelector('form').addEventListener('submit', function(e) {
        const sourceLocation = document.getElementById('source_location_id').value;
        const destinationLocation = document.getElementById('destination_location_id').value;

        if (sourceLocation === destinationLocation) {
            e.preventDefault();
            alert('Source and destination locations cannot be the same.');
            return false;
        }
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>