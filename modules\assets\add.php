<?php
// Start output buffering at the beginning
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/detailed_audit_log.php');

// Add the modern assets CSS
echo '<link rel="stylesheet" href="/choims/assets/css/assets-modern.css">';

// Add custom styles for the SKU modal
echo '<style>
    /* Softer green color variables */
    :root {
        --soft-green-50: #f2f8f2;
        --soft-green-100: #e0f0e0;
        --soft-green-200: #c1e0c1;
        --soft-green-300: #9ecf9e;
        --soft-green-400: #7bbf7b;
        --soft-green-500: #5aad5a;
        --soft-green-600: #4a9c4a;
        --soft-green-700: #3d8b3d;
        --soft-green-800: #327a32;
        --soft-green-900: #276927;
    }

    /* Animation for SKU code update */
    .bg-light-success {
        background-color: var(--soft-green-50) !important;
        transition: background-color 0.5s ease;
    }

    /* Pulse animation for input group */
    @keyframes pulse {
        0% { box-shadow: 0 0 0 0 rgba(90, 173, 90, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(90, 173, 90, 0); }
        100% { box-shadow: 0 0 0 0 rgba(90, 173, 90, 0); }
    }

    .pulse-animation {
        animation: pulse 1.5s ease-in-out;
    }

    /* Highlight pulse for SKU dropdown after creation */
    @keyframes highlight-pulse {
        0% { box-shadow: 0 0 0 0 rgba(90, 173, 90, 0.4); }
        70% { box-shadow: 0 0 0 10px rgba(90, 173, 90, 0); }
        100% { box-shadow: 0 0 0 0 rgba(90, 173, 90, 0); }
    }

    .highlight-pulse {
        animation: highlight-pulse 1.5s ease-in-out;
    }

    /* Modal enhancements */
    .modal-content {
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }

    .bg-gradient-green {
        background: linear-gradient(135deg, var(--soft-green-400) 0%, var(--soft-green-600) 100%);
    }

    /* Form control enhancements */
    .form-control, .form-select {
        border-radius: 1.5rem;
        font-size: 0.95rem;
        padding: 0.6rem 1rem;
        border: 1px solid var(--soft-green-100);
        background-color: var(--soft-green-50);
    }

    .form-control:focus, .form-select:focus {
        box-shadow: 0 0 0 0.25rem rgba(90, 173, 90, 0.15);
        border-color: var(--soft-green-300);
        background-color: white;
    }

    /* Toast positioning */
    .toast {
        z-index: 1100;
        border-radius: 1.5rem;
    }

    /* Button enhancements */
    .btn {
        border-radius: 1.5rem;
        padding: 0.6rem 1.2rem;
        transition: all 0.3s ease;
    }

    .btn-green {
        background: linear-gradient(135deg, var(--soft-green-400) 0%, var(--soft-green-600) 100%);
        border: none;
        color: white;
    }

    .btn-green:hover {
        background: linear-gradient(135deg, var(--soft-green-500) 0%, var(--soft-green-700) 100%);
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-outline-green {
        border: 1px solid var(--soft-green-400);
        color: var(--soft-green-600);
    }

    .btn-outline-green:hover {
        background-color: var(--soft-green-50);
        color: var(--soft-green-700);
        transform: translateY(-2px);
    }

    /* Modal transition */
    .modal.fade .modal-dialog {
        transition: transform 0.3s ease-out;
        transform: scale(0.95);
    }

    .modal.show .modal-dialog {
        transform: scale(1);
    }

    /* Compact form layout */
    .compact-form .row {
        margin-bottom: 0.5rem;
    }

    .compact-form .form-label {
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
        color: var(--soft-green-800);
    }

    .compact-form .form-text {
        margin-top: 0.25rem;
        font-size: 0.75rem;
        color: var(--soft-green-600);
    }

    /* Input group styling */
    .input-group-text {
        border-radius: 1.5rem 0 0 1.5rem;
        background-color: var(--soft-green-100);
        border: 1px solid var(--soft-green-100);
        border-right: none;
        color: var(--soft-green-700);
    }

    .input-group .form-control {
        border-radius: 0 1.5rem 1.5rem 0;
    }

    /* Badge styling */
    .badge-soft-green {
        background-color: var(--soft-green-50);
        color: var(--soft-green-700);
        border-radius: 1rem;
        padding: 0.5rem 0.75rem;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    /* Success toast */
    .toast-success {
        background-color: var(--soft-green-500) !important;
    }

    /* Error toast */
    .toast-error {
        background-color: #e57373 !important;
    }
</style>';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/assets/list.php");
    exit();
}

// Ensure user has appropriate role
requireRole('Logistics');

$success_message = '';
$error_message = '';

// Get categories for dropdown
$categoriesQuery = "SELECT * FROM categories WHERE category_id IN (1, 2, 3) ORDER BY category_name"; // Only IT, Office, Medical Equipment
$categoriesResult = mysqli_query($conn, $categoriesQuery);

// Get sources for dropdown
$sourcesQuery = "SELECT * FROM sources ORDER BY source_name";
$sourcesResult = mysqli_query($conn, $sourcesQuery);

// Get suppliers for dropdown
$suppliersQuery = "SELECT * FROM suppliers ORDER BY supplier_name";
$suppliersResult = mysqli_query($conn, $suppliersQuery);

// Function to generate the next series number
function generateNextSeriesNumber($conn) {
    // Format: LOGIS-MMDDYY-0001
    $today = date('mdy'); // Format: 042825 for April 28, 2025
    $prefix = "LOGIS-{$today}-";

    // Find the latest series number for today
    $query = "SELECT series_number FROM fixed_assets
              WHERE series_number LIKE ?
              ORDER BY series_number DESC LIMIT 1";

    $stmt = mysqli_prepare($conn, $query);
    $searchPattern = $prefix . '%';
    mysqli_stmt_bind_param($stmt, 's', $searchPattern);
    mysqli_stmt_execute($stmt);
    $result = mysqli_stmt_get_result($stmt);

    if (mysqli_num_rows($result) > 0) {
        // Found existing series number for today
        $row = mysqli_fetch_assoc($result);
        $lastSeriesNumber = $row['series_number'];

        // Extract the numeric part
        $numericPart = intval(substr($lastSeriesNumber, strlen($prefix)));

        // Increment and pad with zeros
        $nextNumericPart = $numericPart + 1;
        $nextSeriesNumber = $prefix . str_pad($nextNumericPart, 4, '0', STR_PAD_LEFT);
    } else {
        // No existing series number for today, start with 0001
        $nextSeriesNumber = $prefix . '0001';
    }

    return $nextSeriesNumber;
}

// Get the next series number
$nextSeriesNumber = generateNextSeriesNumber($conn);

// Get locations for dropdown
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);

// Process form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Debug: Log POST data for serial numbers
    $quantity = intval(sanitizeInput($_POST['quantity']));
    if ($quantity > 1) {
        error_log("Processing form with quantity: $quantity");
        // Check for individual serial number fields
        for ($i = 0; $i < $quantity; $i++) {
            $field_name = "serial_number_" . $i;
            if (isset($_POST[$field_name])) {
                error_log("Found serial number for index $i: " . $_POST[$field_name]);
            } else {
                error_log("Missing serial number field for index $i");
            }
        }
    }
    // Validate form inputs
    $sku_id = sanitizeInput($_POST['sku_id']);
    $asset_name = sanitizeInput($_POST['asset_name']);
    $quantity = intval(sanitizeInput($_POST['quantity']));

    // Add maximum limit to prevent performance issues
    $MAX_ASSETS_PER_BATCH = 5000;
    if ($quantity > $MAX_ASSETS_PER_BATCH) {
        $error_message = "For performance reasons, you can only create up to $MAX_ASSETS_PER_BATCH assets at once. Please create them in smaller batches.";
        $quantity = $MAX_ASSETS_PER_BATCH; // Limit the quantity
    }

    $model = sanitizeInput($_POST['model']);
    $specifications = sanitizeInput($_POST['specifications']);
    $status = sanitizeInput($_POST['status']);
    $assigned_to = isset($_POST['assigned_to']) ? sanitizeInput($_POST['assigned_to']) : '';
    $current_location_id = sanitizeInput($_POST['current_location_id']);
    $unit_section = sanitizeInput(isset($_POST['unit_section']) ? $_POST['unit_section'] : '');
    $local_mr = isset($_POST['local_mr']) ? sanitizeInput($_POST['local_mr']) : '';

    // Check if this is for logistics department use
    $for_logistics_use = isset($_POST['for_logistics_use']) ? (bool)$_POST['for_logistics_use'] : false;

    // If it's for logistics use, update status and get additional details
    if ($for_logistics_use) {
        // Force status to "In Use" for logistics department assets
        $status = 'In Use';

        // Get logistics-specific fields
        $logistics_mr_number = isset($_POST['logistics_mr_number']) ? sanitizeInput($_POST['logistics_mr_number']) : '';
        $logistics_staff = isset($_POST['logistics_staff']) ? sanitizeInput($_POST['logistics_staff']) : '';
        $logistics_purpose = isset($_POST['logistics_purpose']) ? sanitizeInput($_POST['logistics_purpose']) : '';

        // Use logistics staff as assigned_to if provided
        if (!empty($logistics_staff)) {
            $assigned_to = $logistics_staff;
        }

        // Use logistics MR number as local_mr if provided
        if (!empty($logistics_mr_number)) {
            $local_mr = $logistics_mr_number;
        }

        // Add purpose to remarks if provided
        if (!empty($logistics_purpose)) {
            $remarks = isset($_POST['remarks']) ? sanitizeInput($_POST['remarks']) : '';
            if (!empty($remarks)) {
                $remarks .= "\n\nPurpose: " . $logistics_purpose;
            } else {
                $remarks = "Purpose: " . $logistics_purpose;
            }
            $_POST['remarks'] = $remarks;
        }
    }

    // Handle empty dates properly
    $purchase_date = !empty($_POST['purchase_date']) ? sanitizeInput($_POST['purchase_date']) : null;
    $unit_cost = (isset($_POST['unit_cost']) && $_POST['unit_cost'] !== '') ? sanitizeInput($_POST['unit_cost']) : '0';
    $amount = $quantity * (floatval($unit_cost) ?: 0); // Calculate amount server-side for accuracy
    $receipt_type = !empty($_POST['receipt_type']) ? sanitizeInput($_POST['receipt_type']) : null;

    // Map "Local MR" to "D.R" for database compatibility
    if ($receipt_type === 'Local MR') {
        $receipt_type = 'D.R';
    }

    // Handle series number - use auto-generated or manual override
    if (isset($_POST['override_series_number']) && $_POST['override_series_number'] == 'on') {
        // User has chosen to override the auto-generated series number
        $series_number = sanitizeInput($_POST['series_number']);
    } else {
        // Use the auto-generated series number
        $series_number = $nextSeriesNumber;
    }

    // Handle supplier - check if "Others" is selected
    $supplier_id = !empty($_POST['supplier_id']) ? sanitizeInput($_POST['supplier_id']) : null;
    $other_supplier = isset($_POST['other_supplier']) ? sanitizeInput($_POST['other_supplier']) : null;

    // If supplier is "Others" and a custom value is provided, create a new supplier
    if ($supplier_id && !empty($other_supplier)) {
        // Execute query and store result in a variable
        $supplierQuery = mysqli_query($conn, "SELECT supplier_name FROM suppliers WHERE supplier_id = $supplier_id");
        if ($supplierQuery) {
            $supplierData = mysqli_fetch_assoc($supplierQuery);
            if ($supplierData && $supplierData['supplier_name'] == 'Others') {
                // Insert new supplier - only use supplier_name as supplier_code doesn't exist
                $insertSupplierQuery = "INSERT INTO suppliers (supplier_name) VALUES (?)";
                $supplierStmt = mysqli_prepare($conn, $insertSupplierQuery);
                if (!$supplierStmt) {
                    // Log the error for debugging
                    error_log("Prepare failed for supplier insert: " . mysqli_error($conn));
                } else {
                    mysqli_stmt_bind_param($supplierStmt, 's', $other_supplier);
                    if (mysqli_stmt_execute($supplierStmt)) {
                        $supplier_id = mysqli_insert_id($conn);
                    } else {
                        error_log("Execute failed for supplier insert: " . mysqli_stmt_error($supplierStmt));
                    }
                    mysqli_stmt_close($supplierStmt);
                }
            }
        }
    }

    // Handle source - check if "Other Agencies" is selected
    $source_id = !empty($_POST['source_id']) ? sanitizeInput($_POST['source_id']) : null;
    $other_source = isset($_POST['other_source']) ? sanitizeInput($_POST['other_source']) : null;

    // If source is "Other Agencies" and a custom value is provided, add it to remarks instead of creating a new source
    if ($source_id && !empty($other_source)) {
        // Execute query and store result in a variable
        $sourceQuery = mysqli_query($conn, "SELECT source_name FROM sources WHERE source_id = $source_id");
        if ($sourceQuery) {
            $sourceData = mysqli_fetch_assoc($sourceQuery);
            if ($sourceData && $sourceData['source_name'] == 'Other Agencies') {
                // Add the custom source name to remarks instead of creating a new source
                $remarks = sanitizeInput($_POST['remarks']);
                if (!empty($remarks)) {
                    $remarks .= "\n\nSource Agency: " . $other_source;
                } else {
                    $remarks = "Source Agency: " . $other_source;
                }
                $_POST['remarks'] = $remarks;

                // Keep using the "Other Agencies" source_id
                // No need to create a new source entry
            }
        }
    }

    // Handle empty warranty date properly
    $warranty_expiry = !empty($_POST['warranty_expiry']) ? sanitizeInput($_POST['warranty_expiry']) : null;
    // Make sure to get the updated remarks that might have been modified for Other Agencies
    $remarks = sanitizeInput($_POST['remarks']);

    // Check if amount column exists in fixed_assets table
    $columnExistsQuery = "SHOW COLUMNS FROM fixed_assets LIKE 'amount'";
    $columnExistsResult = mysqli_query($conn, $columnExistsQuery);
    $amountColumnExists = mysqli_num_rows($columnExistsResult) > 0;

    // Check if required fields are not empty
    if (empty($sku_id) || empty($asset_name) || empty($current_location_id) || $quantity < 1) {
        $error_message = "Please fill in all required fields.";
    } else {
        $success_count = 0;
        $error_count = 0;
        $asset_ids = [];

        // Ensure dates are not empty strings which might cause 0000-00-00 in database
        $purchase_date = (!empty($purchase_date) && $purchase_date !== '0000-00-00') ? $purchase_date : null;
        $warranty_expiry = (!empty($warranty_expiry) && $warranty_expiry !== '0000-00-00') ? $warranty_expiry : null;

        // Handle empty numeric values properly
        $unit_cost = (isset($_POST['unit_cost']) && $_POST['unit_cost'] !== '') ? sanitizeInput($_POST['unit_cost']) : '0';
        $amount = $quantity * (floatval($unit_cost) ?: 0); // Calculate amount server-side for accuracy

        // Handle creating multiple assets if quantity > 1
        for ($i = 0; $i < $quantity; $i++) {
            // Get serial number for this asset - handle different approaches
            $serial_number = '';

            // 1. Check if we have JSON data from file upload
            if (isset($_POST['serial_numbers_json']) && !empty($_POST['serial_numbers_json'])) {
                $serial_numbers = json_decode($_POST['serial_numbers_json'], true);
                if (is_array($serial_numbers) && isset($serial_numbers[$i])) {
                    $serial_number = sanitizeInput($serial_numbers[$i]);
                } else {
                    // Fallback if JSON is invalid or index doesn't exist
                    $serial_number = "ASSET-" . ($i + 1);
                }
            }
            // 2. Check if we're using the prefix approach
            else if (isset($_POST['serial_prefix']) && !empty($_POST['serial_prefix'])) {
                $serial_number = sanitizeInput($_POST['serial_prefix']) . ($i + 1);
            }
            // 3. Fall back to the traditional approach with individual fields
            else {
                if ($quantity > 1) {
                    $field_name = "serial_number_" . $i;
                    if (isset($_POST[$field_name])) {
                        $serial_number = sanitizeInput($_POST[$field_name]);
                    } else {
                        // If field doesn't exist, use a default value
                        $serial_number = "ASSET-" . ($i + 1);
                        // Log for debugging
                        error_log("Serial number field $field_name not found in POST data");
                    }
                } else {
                    $serial_number = sanitizeInput($_POST['serial_number']);
                }
            }

            // Ensure we never save an empty serial number
            if (empty($serial_number)) {
                $serial_number = "ASSET-" . ($i + 1);
                error_log("Empty serial number detected for asset #" . ($i + 1) . ", using default value");
            }

            // Generate a unique series number for each asset if we're creating multiple
            if ($quantity > 1) {
                // For multiple assets, increment the series number for each one
                if ($i > 0) {
                    // Parse the current series number to extract the numeric part
                    $prefix = '';
                    $numericPart = 0;

                    if (preg_match('/^(.*?)(\d+)$/', $series_number, $matches)) {
                        $prefix = $matches[1];
                        $numericPart = intval($matches[2]);
                    } else {
                        // If we can't parse it, just append the index
                        $prefix = $series_number . '-';
                        $numericPart = 0;
                    }

                    // Increment and create the new series number
                    $numericPart++;
                    $series_number = $prefix . str_pad($numericPart, 4, '0', STR_PAD_LEFT);
                }
            }

            // Format asset name for multiple assets
            $formatted_asset_name = $asset_name;
            if ($quantity > 1) {
                $formatted_asset_name .= ' #' . ($i + 1);
            }

            // Escape all values for direct SQL insertion
            $sku_id_safe = mysqli_real_escape_string($conn, $sku_id);
            $formatted_asset_name_safe = mysqli_real_escape_string($conn, $formatted_asset_name);
            $serial_number_safe = mysqli_real_escape_string($conn, $serial_number);
            $model_safe = mysqli_real_escape_string($conn, $model);
            $specifications_safe = mysqli_real_escape_string($conn, $specifications);
            $status_safe = mysqli_real_escape_string($conn, $status);
            $assigned_to_safe = mysqli_real_escape_string($conn, $assigned_to);
            $current_location_id_safe = mysqli_real_escape_string($conn, $current_location_id);
            $unit_section_safe = mysqli_real_escape_string($conn, $unit_section);
            $local_mr_safe = mysqli_real_escape_string($conn, $local_mr);

            // Handle date values - use explicit NULL for empty dates
            $purchase_date_sql = empty($purchase_date) ? "NULL" : "'" . mysqli_real_escape_string($conn, $purchase_date) . "'";
            $warranty_expiry_sql = empty($warranty_expiry) ? "NULL" : "'" . mysqli_real_escape_string($conn, $warranty_expiry) . "'";

            // Handle numeric values - use explicit NULL for empty values
            $unit_cost_sql = ($unit_cost === '' || $unit_cost === null) ? "'0'" : "'" . mysqli_real_escape_string($conn, $unit_cost) . "'";
            $amount_safe = mysqli_real_escape_string($conn, $amount);
            $amount_sql = "'" . $amount_safe . "'";
            $receipt_type_sql = $receipt_type === null ? "NULL" : "'" . mysqli_real_escape_string($conn, $receipt_type) . "'";
            // Handle empty series_number properly
            $series_number_safe = empty($series_number) ? "NULL" : "'" . mysqli_real_escape_string($conn, $series_number) . "'";
            $supplier_id_sql = empty($supplier_id) ? "NULL" : "'" . mysqli_real_escape_string($conn, $supplier_id) . "'";
            $source_id_sql = empty($source_id) ? "NULL" : "'" . mysqli_real_escape_string($conn, $source_id) . "'";
            $remarks_safe = mysqli_real_escape_string($conn, $remarks);
            $user_id = $_SESSION['user_id'];

            // Direct SQL query
            if ($amountColumnExists) {
                $directSQL = "
                    INSERT INTO fixed_assets (
                        sku_id, asset_name, serial_number, model, specifications, status,
                        assigned_to, current_location_id, unit_section, local_mr, purchase_date,
                        unit_cost, amount, receipt_type, series_number, supplier_id, warranty_expiry,
                        source_id, remarks, created_by, created_at
                    ) VALUES (
                        '$sku_id_safe', '$formatted_asset_name_safe', '$serial_number_safe', '$model_safe',
                        '$specifications_safe', '$status_safe', '$assigned_to_safe', '$current_location_id_safe',
                        '$unit_section_safe', '$local_mr_safe', $purchase_date_sql, $unit_cost_sql,
                        $amount_sql, $receipt_type_sql, $series_number_safe, $supplier_id_sql,
                        $warranty_expiry_sql, $source_id_sql, '$remarks_safe', '$user_id', NOW()
                    )
                ";
            } else {
                $directSQL = "
                    INSERT INTO fixed_assets (
                        sku_id, asset_name, serial_number, model, specifications, status,
                        assigned_to, current_location_id, unit_section, local_mr, purchase_date,
                        unit_cost, receipt_type, series_number, supplier_id, warranty_expiry,
                        source_id, remarks, created_by, created_at
                    ) VALUES (
                        '$sku_id_safe', '$formatted_asset_name_safe', '$serial_number_safe', '$model_safe',
                        '$specifications_safe', '$status_safe', '$assigned_to_safe', '$current_location_id_safe',
                        '$unit_section_safe', '$local_mr_safe', $purchase_date_sql, $unit_cost_sql,
                        $receipt_type_sql, $series_number_safe, $supplier_id_sql,
                        $warranty_expiry_sql, $source_id_sql, '$remarks_safe', '$user_id', NOW()
                    )
                ";
            }

            // Execute the direct SQL query
            if (mysqli_query($conn, $directSQL)) {
                $asset_id = mysqli_insert_id($conn);
                $asset_ids[] = $asset_id;
                $success_count++;

                // Create detailed audit log
                $asset_data = [
                    'sku_id' => $sku_id,
                    'asset_name' => $formatted_asset_name_safe,
                    'serial_number' => $serial_number_safe,
                    'model' => $model_safe,
                    'specifications' => $specifications_safe,
                    'status' => $status_safe,
                    'assigned_to' => $assigned_to_safe,
                    'current_location_id' => $current_location_id_safe,
                    'unit_section' => $unit_section_safe,
                    'local_mr' => $local_mr_safe,
                    'purchase_date' => $purchase_date,
                    'unit_cost' => $unit_cost,
                    'receipt_type' => $receipt_type,
                    'series_number' => $series_number_safe,
                    'supplier_id' => $supplier_id,
                    'warranty_expiry' => $warranty_expiry,
                    'source_id' => $source_id,
                    'remarks' => $remarks_safe
                ];

                // Log to both systems for backward compatibility
                logActivity($conn, "Created asset", "fixed_assets", $asset_id, null, null);
                logFixedAssetAction($conn, $user_id, 'create', $asset_id, null, $asset_data);

            } else {
                $error_count++;
                error_log("Error creating asset: " . mysqli_error($conn));
            }
        }

        // Set success message
        if ($success_count > 0) {
            if ($quantity > 1) {
                $success_message = "$success_count assets created successfully!";

                // Redirect to the assets list page when multiple assets are created
                header("Location: /choims/modules/assets/list.php?success=true");
                exit;
            } else {
                $success_message = "Asset created successfully!";

                // Get the first (and only) asset ID
                $asset_id = $asset_ids[0];

                // Check if transfer was requested
                $transfer_location_id = sanitizeInput($_POST['transfer_location_id'] ?? '');
                if (!empty($transfer_location_id)) {
                    // Get assigned_to and local_mr values if provided
                    $assigned_to = !empty($_POST['assigned_to']) ? sanitizeInput($_POST['assigned_to']) : '';
                    $local_mr = !empty($_POST['local_mr']) ? sanitizeInput($_POST['local_mr']) : '';

                    // Build redirect URL with parameters
                    $redirect_url = "/choims/modules/transfers/initiate.php?asset_id=$asset_id";

                    // Add destination location
                    $redirect_url .= "&destination_location_id=" . urlencode($transfer_location_id);

                    // Add notes parameter if assigned_to is provided
                    if (!empty($assigned_to)) {
                        $redirect_url .= "&notes=" . urlencode("Assigned to: $assigned_to");
                    }

                    // Add reference document parameter if local_mr is provided
                    if (!empty($local_mr)) {
                        $redirect_url .= "&reference_document=" . urlencode($local_mr);
                    }

                    // Redirect to transfer initiation page
                    header("Location: $redirect_url");
                    exit;
                } else {
                    // Redirect to the view page for a single asset
                    header("Location: /choims/modules/assets/view.php?id=$asset_id&success=true");
                    exit;
                }
            }
        }

        if ($error_count > 0) {
            $error_message = "$error_count assets failed to create. Please check the logs for details.";
        }
    }
}
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title"><i class="fas fa-plus-circle"></i>Add New Asset</h1>
                <p class="page-subtitle">Register a new fixed asset in the inventory system</p>
            </div>
            <div class="col-md-4 text-md-end">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="/choims/dashboard.php">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="/choims/modules/assets/index.php">Assets</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Add New</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <?php if (isset($success_message) && !empty($success_message)): ?>
        <div class="alert alert-success">
            <div class="d-flex align-items-center">
                <div class="alert-icon">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div>
                    <h5 class="mb-1">Success!</h5>
                    <p class="mb-0"><?php echo $success_message; ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <?php if (isset($error_message) && !empty($error_message)): ?>
        <div class="alert alert-danger">
            <div class="d-flex align-items-center">
                <div class="alert-icon">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div>
                    <h5 class="mb-1">Error!</h5>
                    <p class="mb-0"><?php echo $error_message; ?></p>
                </div>
            </div>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-12">
            <form method="post" action="" id="assetForm" class="needs-validation" novalidate>
                <div class="row">
                    <div class="col-lg-8">
                        <!-- Asset Basic Information -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-info-circle"></i>
                                <h5 class="card-header-title">Basic Information</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                            <div class="input-group">
                                <select class="form-select" id="sku_id" name="sku_id" required>
                                    <option value="">Select SKU</option>
                                    <?php
                                    // Get SKUs for fixed assets
                                    $skuQuery = "SELECT s.* FROM sku_master s WHERE s.item_type = 'Fixed' ORDER BY s.sku_code";
                                    $skuResult = mysqli_query($conn, $skuQuery);

                                    while ($sku = mysqli_fetch_assoc($skuResult)) {
                                        echo "<option value='" . $sku['sku_id'] . "'>" . $sku['sku_code'] . " - " . $sku['sku_name'] . "</option>";
                                    }
                                    ?>
                                </select>
                                <button class="btn btn-outline-primary" type="button" id="add_new_sku_btn" data-bs-toggle="modal" data-bs-target="#addSkuModal">
                                    <i class="fas fa-plus"></i> New
                                </button>
                            </div>
                            <label for="sku_id"></label>
                            <div class="invalid-feedback">Please select an SKU</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="asset_name" name="asset_name" required>
                                            <label for="asset_name">Equipment/Item Name <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please enter item name</div>
                        </div>
                    </div>
                </div>
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="serial_number" name="serial_number">
                                            <label for="serial_number">Serial Number/MEI#</label>
                                            <div class="invalid-feedback">Please enter a serial number</div>
                                        </div>
                                    </div>
                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="model" name="model" required>
                                            <label for="model">Model</label>
                                            <div class="invalid-feedback">Please enter a model</div>
                        </div>
                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input type="number" class="form-control" id="quantity" name="quantity" min="1" value="1" required>
                                            <label for="quantity">Quantity <span class="text-danger">*</span></label>
                                            <div class="invalid-feedback">Please enter a valid quantity</div>
                        </div>
                    </div>
                </div>

                <!-- Container for multiple serial numbers when quantity > 1 -->
                <div id="serial_numbers_container" class="mb-3" style="display: none;">
                    <div class="card bg-light border-0">
                        <div class="card-header bg-light d-flex align-items-center">
                            <i class="fas fa-list-ol text-primary me-2"></i>
                            <h6 class="mb-0">Individual Serial Numbers</h6>
                        </div>
                        <div class="card-body" id="serial_numbers_fields">
                            <!-- Dynamic serial number fields will be added here -->
                        </div>
                    </div>
                </div>

                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <textarea class="form-control" id="specifications" name="specifications" style="height: 100px" required></textarea>
                                            <label for="specifications">Specifications</label>
                                            <div class="invalid-feedback">Please enter specifications</div>
                                        </div>
                                    </div>
                        </div>
                    </div>
                </div>

                        <!-- Asset Status and Location Information -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-map-marker-alt"></i>
                                <h5 class="card-header-title">Status & Location</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="status" name="status" required>
                                                <option value="In Use">In Use</option>
                                                <option value="Available" selected>Available</option>
                                                <option value="Under Repair">Under Repair</option>
                                                <option value="Damaged">Damaged</option>
                                                <option value="Retired">Retired</option>
                                            </select>
                                            <label for="status">Status <span class="text-danger">*</span></label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <?php
                                            mysqli_data_seek($locationsResult, 0);
                                            $logisticsLocationId = null;
                                            $logisticsLocationName = "PROPERTY AND SUPPLY/LOGISTIC MANAGEMENT UNIT";

                                            // Find the Property and Supply/Logistics location
                                            while ($location = mysqli_fetch_assoc($locationsResult)) {
                                                // Check if the location matches Property and Supply/Logistics Department
                                                $isLogisticsLocation = (stripos($location['location_name'], 'PROPERTY AND SUPPLY') !== false ||
                                                                       stripos($location['location_name'], 'LOGISTICS') !== false);

                                                if ($isLogisticsLocation) {
                                                    $logisticsLocationId = $location['location_id'];
                                                    $logisticsLocationName = $location['location_name'];
                                                    break;
                                                }
                                            }
                                            ?>
                                            <input type="text" class="form-control" value="<?php echo $logisticsLocationName; ?>" readonly>
                                            <input type="hidden" id="current_location_id" name="current_location_id" value="<?php echo $logisticsLocationId; ?>">
                                            <label for="current_location_id">Current Location <span class="text-danger">*</span></label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="assigned_to" name="assigned_to">
                                            <label for="assigned_to">Assigned To</label>
                                            <div class="invalid-feedback">Please specify who this asset is assigned to.</div>
                                        </div>
                                    </div>
                                </div>

                                <!-- For Logistics Department Use Checkbox -->
                                <div class="row g-3 mb-3">
                                    <div class="col-md-12">
                                        <div class="card border-0 bg-light mb-0">
                                            <div class="card-body py-3">
                                                <div class="d-flex align-items-center">
                                                    <div class="form-check form-switch me-3">
                                                        <input class="form-check-input" type="checkbox" id="for_logistics_use" name="for_logistics_use" value="1"
                                                               style="width: 3rem; height: 1.5rem;">
                                                    </div>
                                                    <div class="d-flex align-items-center">
                                                        <i class="fas fa-warehouse text-primary me-2" style="font-size: 1.2rem;"></i>
                                                        <label class="form-check-label mb-0" for="for_logistics_use">
                                                            <span class="fw-bold">This asset will be used by Property, Logistics, and Supply Management</span>
                                                            <br>
                                                            <small class="text-muted">Check this option if the asset will remain with the logistics department</small>
                                                        </label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <input type="text" class="form-control" id="unit_section" name="unit_section" value="PROPERTY AND SUPPLY/LOGISTIC MANAGEMENT UNIT" readonly>
                                            <label for="unit_section">Unit/Section/Department</label>
                                        </div>
                                    </div>
                                </div>

                                <div class="row g-3">
                                    <div class="col-md-12">
                                        <div class="form-floating mb-3">
                                            <select class="form-select" id="transfer_location_id" name="transfer_location_id">
                                                <option value="">No Transfer - Keep at Current Location</option>
                                                <?php
                                                mysqli_data_seek($locationsResult, 0);
                                                while ($location = mysqli_fetch_assoc($locationsResult)) {
                                                    echo "<option value='" . $location['location_id'] . "'>" . $location['location_name'] . "</option>";
                                                }
                                                ?>
                                            </select>
                                            <label for="transfer_location_id">Initiate Transfer To (Optional)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- Submit Button added below Status & Location -->
                            <div class="card-footer text-center">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-save me-2"></i>Save Asset
                                </button>
                                <button type="reset" class="btn btn-outline-secondary btn-lg ms-3">
                                    <i class="fas fa-undo me-2"></i>Reset
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-4">
                        <!-- Acquisition Information -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-coins"></i>
                                <h5 class="card-header-title">Acquisition Details</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="purchase_date" name="purchase_date" value="<?php echo date('Y-m-d'); ?>">
                                    <label for="purchase_date">Purchase Date</label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input type="number" step="0.01" class="form-control" id="unit_cost" name="unit_cost" value="0">
                                    <label for="unit_cost">Unit Cost</label>
                                </div>
                                <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="amount" name="amount" readonly>
                                    <label for="amount">Total Amount</label>
                        </div>
                                <div class="form-floating mb-3">
                            <select class="form-select" id="receipt_type" name="receipt_type">
                                <option value="">Select receipt type</option>
                                <option value="Local MR">Local MR</option>
                                <option value="P.T.R">P.T.R</option>
                                <option value="O.R">O.R</option>
                            </select>
                                    <label for="receipt_type">Receipt Type</label>
                                    <div class="invalid-feedback">Required when initiating a transfer. Please select a receipt type.</div>
                                </div>
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="series_number" name="series_number" value="<?php echo htmlspecialchars($nextSeriesNumber); ?>" readonly>
                                    <label for="series_number">Series # (Auto-generated)</label>
                                    <div class="invalid-feedback">Please enter a series number</div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="override_series_number" name="override_series_number">
                                    <label class="form-check-label" for="override_series_number">
                                        Override auto-generated series number
                                    </label>
                                </div>
                                <div class="form-floating mb-3">
                                    <input type="text" class="form-control" id="local_mr" name="local_mr">
                                    <label for="local_mr">Local MR</label>
                                    <div class="invalid-feedback">Required when initiating a transfer. Please enter a local MR.</div>
                                </div>
                                <div class="form-floating mb-3">
                                    <input type="date" class="form-control" id="warranty_expiry" name="warranty_expiry">
                                    <label for="warranty_expiry">Warranty Expiry Date</label>
                                </div>
                            </div>
                        </div>

                        <!-- Source and Supplier -->
                        <div class="card">
                            <div class="card-header">
                                <i class="fas fa-truck"></i>
                                <h5 class="card-header-title">Source & Supplier</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-floating mb-3">
                            <select class="form-select" id="source_id" name="source_id" required>
                                <option value="">Select Source</option>
                                <?php
                                        mysqli_data_seek($sourcesResult, 0);
                                while ($source = mysqli_fetch_assoc($sourcesResult)) {
                                    echo "<option value='" . $source['source_id'] . "'>" . $source['source_name'] . "</option>";
                                }
                                ?>
                            </select>
                                    <label for="source_id">Source <span class="text-danger">*</span></label>
                                    <div class="invalid-feedback">Please select a source</div>
                                </div>
                                <div id="other_source_container" class="mb-3" style="display:none;">
                                    <div class="form-floating">
                                <input type="text" class="form-control" id="other_source" name="other_source" placeholder="Enter source name">
                                        <label for="other_source">Other Source</label>
                            </div>
                        </div>
                                <div class="form-floating mb-3">
                                    <select class="form-select" id="supplier_id" name="supplier_id" required>
                                        <option value="">Select Supplier</option>
                                <?php
                                        mysqli_data_seek($suppliersResult, 0);
                                        while ($supplier = mysqli_fetch_assoc($suppliersResult)) {
                                            echo "<option value='" . $supplier['supplier_id'] . "'>" . $supplier['supplier_name'] . "</option>";
                                }
                                ?>
                            </select>
                                    <label for="supplier_id">Supplier <span class="text-danger">*</span></label>
                                    <div class="invalid-feedback">Please select a supplier</div>
                                </div>
                                <div id="other_supplier_container" class="mb-3" style="display:none;">
                                    <div class="form-floating">
                                        <input type="text" class="form-control" id="other_supplier" name="other_supplier" placeholder="Enter supplier name">
                                        <label for="other_supplier">Other Supplier</label>
                                    </div>
                                </div>
                                <div class="form-floating mb-3">
                                    <textarea class="form-control" id="remarks" name="remarks" style="height: 100px"></textarea>
                                    <label for="remarks">Remarks</label>
                                    <div class="invalid-feedback">Please enter remarks</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="text-center mt-4 mb-5">
                    <!-- Reset button moved next to Save Asset button -->
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Styles moved to assets/css/assets-modern.css -->

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Calculate amount based on quantity and unit cost
    const unitCostInput = document.getElementById('unit_cost');
    const quantityInput = document.getElementById('quantity');
    const amountInput = document.getElementById('amount');
    const serialNumbersContainer = document.getElementById('serial_numbers_container');
    const serialNumbersFields = document.getElementById('serial_numbers_fields');
    const singleSerialField = document.getElementById('serial_number');
    const currentLocationSelect = document.getElementById('current_location_id');
    const transferLocationSelect = document.getElementById('transfer_location_id');
    const assignedToInput = document.getElementById('assigned_to');
    const localMRInput = document.getElementById('local_mr');
    const receiptTypeSelect = document.getElementById('receipt_type');

    // Series number override functionality
    const seriesNumberInput = document.getElementById('series_number');
    const overrideSeriesCheckbox = document.getElementById('override_series_number');

    // Handle series number override checkbox
    overrideSeriesCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Enable manual editing of series number
            seriesNumberInput.readOnly = false;
            seriesNumberInput.focus();
            // Add a visual indicator that the field is editable
            seriesNumberInput.classList.add('border-warning');
            // Change the label to indicate it's being manually edited
            seriesNumberInput.parentElement.querySelector('label').textContent = 'Series # (Manual)';
        } else {
            // Reset to auto-generated value
            seriesNumberInput.readOnly = true;
            seriesNumberInput.value = '<?php echo htmlspecialchars($nextSeriesNumber); ?>';
            // Remove the visual indicator
            seriesNumberInput.classList.remove('border-warning');
            // Reset the label
            seriesNumberInput.parentElement.querySelector('label').textContent = 'Series # (Auto-generated)';
        }
    });

    // Logistics department use elements
    const forLogisticsUseCheckbox = document.getElementById('for_logistics_use');

    // Simpler date input enhancement
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(function(dateInput) {
        // Add click event to the parent form-floating element
        const parent = dateInput.closest('.form-floating');
        if (parent) {
            parent.style.cursor = 'pointer';
            parent.addEventListener('click', function(e) {
                // Only process if we didn't click directly on the input
                // This prevents double triggering
                if (e.target !== dateInput) {
                    if (typeof dateInput.showPicker === 'function') {
                        dateInput.showPicker();
                    } else {
                        dateInput.focus();
                        dateInput.click();
                    }
                }
            });
        }
    });

    // Handle Logistics Department Use checkbox
    forLogisticsUseCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Set status to "In Use" automatically
            document.getElementById('status').value = 'In Use';

            // Enable the assigned_to, local_mr, and receipt_type fields
            assignedToInput.disabled = false;
            localMRInput.disabled = false;
            receiptTypeSelect.disabled = false;

            // Remove opacity styling
            assignedToInput.parentElement.classList.remove('opacity-75');
            localMRInput.parentElement.classList.remove('opacity-75');
            receiptTypeSelect.parentElement.classList.remove('opacity-75');

            // Add highlight styling to show these fields are related to logistics use
            assignedToInput.parentElement.classList.add('shadow-sm');
            localMRInput.parentElement.classList.add('shadow-sm');
            receiptTypeSelect.parentElement.classList.add('shadow-sm');

            // Add a subtle background color
            assignedToInput.style.backgroundColor = '#fafafa';
            localMRInput.style.backgroundColor = '#fafafa';
            receiptTypeSelect.style.backgroundColor = '#fafafa';

            // Update labels to show required fields
            assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To <span class="text-danger">*</span>';
            localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR <span class="text-danger">*</span>';
            receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type <span class="text-danger">*</span>';

            // Set required attributes
            assignedToInput.setAttribute('required', 'required');
            localMRInput.setAttribute('required', 'required');
            receiptTypeSelect.setAttribute('required', 'required');

            // Add a tooltip to explain these fields
            $(assignedToInput).attr('title', 'Staff member using this asset').tooltip();
            $(localMRInput).attr('title', 'MR number for logistics department use').tooltip();
            $(receiptTypeSelect).attr('title', 'Type of receipt for this asset').tooltip();
        } else {
            // Only disable fields if no transfer location is selected
            if (!transferLocationSelect.value) {
                // Disable fields
                assignedToInput.disabled = true;
                localMRInput.disabled = true;
                receiptTypeSelect.disabled = true;

                // Add opacity styling
                assignedToInput.parentElement.classList.add('opacity-75');
                localMRInput.parentElement.classList.add('opacity-75');
                receiptTypeSelect.parentElement.classList.add('opacity-75');

                // Remove highlight styling
                assignedToInput.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');
                localMRInput.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');
                receiptTypeSelect.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');

                // Remove background color
                assignedToInput.style.backgroundColor = '';
                localMRInput.style.backgroundColor = '';
                receiptTypeSelect.style.backgroundColor = '';

                // Update labels to remove required indicator
                assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To';
                localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR';
                receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type';

                // Remove required attributes
                assignedToInput.removeAttribute('required');
                localMRInput.removeAttribute('required');
                receiptTypeSelect.removeAttribute('required');

                // Clear values
                assignedToInput.value = '';
                localMRInput.value = '';
                receiptTypeSelect.selectedIndex = 0;

                // Remove tooltips
                $(assignedToInput).tooltip('dispose');
                $(localMRInput).tooltip('dispose');
                $(receiptTypeSelect).tooltip('dispose');
            }
        }
    });

    // Only disable transfer-related fields by default
    if (!transferLocationSelect.value) {
        assignedToInput.disabled = true;
        localMRInput.disabled = true;
        receiptTypeSelect.disabled = true;

        // Add styling to show they're disabled
        assignedToInput.parentElement.classList.add('opacity-75');
        localMRInput.parentElement.classList.add('opacity-75');
        receiptTypeSelect.parentElement.classList.add('opacity-75');
    }

    // Handle transfer location changes
    transferLocationSelect.addEventListener('change', function() {
        // If logistics use is checked, don't disable assigned_to and local_mr
        const isLogisticsUse = forLogisticsUseCheckbox.checked;

        if (this.value) {
            // Transfer location selected, enable fields
            assignedToInput.disabled = false;
            localMRInput.disabled = false;
            receiptTypeSelect.disabled = false;
            assignedToInput.parentElement.classList.remove('opacity-75');
            localMRInput.parentElement.classList.remove('opacity-75');
            receiptTypeSelect.parentElement.classList.remove('opacity-75');

            // Update labels to show required fields
            assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To <span class="text-danger">*</span>';
            localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR <span class="text-danger">*</span>';
            receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type <span class="text-danger">*</span>';

            // Set required attributes
            assignedToInput.setAttribute('required', 'required');
            localMRInput.setAttribute('required', 'required');
            receiptTypeSelect.setAttribute('required', 'required');

            // If logistics use is checked, uncheck it as we're transferring the asset
            if (isLogisticsUse) {
                forLogisticsUseCheckbox.checked = false;

                // Remove logistics-specific styling
                assignedToInput.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');
                localMRInput.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');
                receiptTypeSelect.parentElement.classList.remove('border-start', 'border-primary', 'border-3', 'ps-3');

                // Remove background color
                assignedToInput.style.backgroundColor = '';
                localMRInput.style.backgroundColor = '';
                receiptTypeSelect.style.backgroundColor = '';

                // Remove tooltips
                $(assignedToInput).tooltip('dispose');
                $(localMRInput).tooltip('dispose');
                $(receiptTypeSelect).tooltip('dispose');
            }

            // Add transfer-specific styling
            assignedToInput.parentElement.classList.add('border-start', 'border-success', 'border-3', 'ps-3');
            localMRInput.parentElement.classList.add('border-start', 'border-success', 'border-3', 'ps-3');
            receiptTypeSelect.parentElement.classList.add('border-start', 'border-success', 'border-3', 'ps-3');

            // Add a subtle background color for transfer
            assignedToInput.style.backgroundColor = '#f8fff9';
            localMRInput.style.backgroundColor = '#f8fff9';
            receiptTypeSelect.style.backgroundColor = '#f8fff9';
        } else {
            // No transfer location, check if logistics use is checked
            if (!isLogisticsUse) {
                // Only disable fields if not for logistics use
                assignedToInput.disabled = true;
                localMRInput.disabled = true;
                receiptTypeSelect.disabled = true;
                assignedToInput.parentElement.classList.add('opacity-75');
                localMRInput.parentElement.classList.add('opacity-75');
                receiptTypeSelect.parentElement.classList.add('opacity-75');

                // Remove transfer-specific styling
                assignedToInput.parentElement.classList.remove('border-start', 'border-success', 'border-3', 'ps-3');
                localMRInput.parentElement.classList.remove('border-start', 'border-success', 'border-3', 'ps-3');
                receiptTypeSelect.parentElement.classList.remove('border-start', 'border-success', 'border-3', 'ps-3');

                // Remove background color
                assignedToInput.style.backgroundColor = '';
                localMRInput.style.backgroundColor = '';
                receiptTypeSelect.style.backgroundColor = '';

                // Update labels to remove required indicator
                assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To';
                localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR';
                receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type';

                // Remove required attributes
                assignedToInput.removeAttribute('required');
                localMRInput.removeAttribute('required');
                receiptTypeSelect.removeAttribute('required');

                // Clear values when disabled
                assignedToInput.value = '';
                localMRInput.value = '';
                receiptTypeSelect.selectedIndex = 0;
            }
        }

        // Important: Make sure all other form fields are enabled
        enableAllOtherFormFields();
    });

    // Function to ensure all other form fields are enabled
    function enableAllOtherFormFields() {
        // Get all input elements in the form
        const allInputs = document.querySelectorAll('#assetForm input, #assetForm select, #assetForm textarea');

        // Enable each input unless it's specifically meant to be disabled
        allInputs.forEach(input => {
            // Skip the transfer-specific fields which have their own logic
            if (input.id === 'assigned_to' || input.id === 'local_mr' || input.id === 'receipt_type') {
                return;
            }

            // Skip the amount field which should always be readonly
            if (input.id === 'amount') {
                return;
            }

            // Skip the serial_number field in multi-quantity case
            if (input.id === 'serial_number' && parseInt(quantityInput.value) > 1) {
                return;
            }

            // Enable this input
            input.disabled = false;
        });
    }

    // Call this on page load to ensure all fields are properly enabled
    enableAllOtherFormFields();

    // Trigger the change event on current_location_id to ensure everything is initialized properly
    if (currentLocationSelect && currentLocationSelect.value) {
        const event = new Event('change');
        currentLocationSelect.dispatchEvent(event);
    }

    function calculateAmount() {
        const unitCost = parseFloat(unitCostInput.value) || 0;
        const quantity = parseInt(quantityInput.value) || 0;
        amountInput.value = (unitCost * quantity).toFixed(2);
    }

    // Initialize amount on page load
    calculateAmount();

    function updateSerialNumberFields(preserveValues = false) {
        const quantity = parseInt(quantityInput.value) || 0;

        // Store existing values if we need to preserve them
        let existingValues = {};
        if (preserveValues) {
            // Collect existing serial number values
            const existingInputs = serialNumbersFields.querySelectorAll('input[name^="serial_number_"]');
            existingInputs.forEach(input => {
                const index = input.name.replace('serial_number_', '');
                existingValues[index] = input.value;
            });
        }

        // Clear previous fields
        serialNumbersFields.innerHTML = '';

        if (quantity > 1) {
            // Show container and hide single field
            serialNumbersContainer.style.display = 'block';
            singleSerialField.disabled = true;
            singleSerialField.parentElement.classList.add('opacity-50');

            // Add a note to inform the user
            const note = document.createElement('div');
            note.className = 'alert alert-info mb-3';

            // Check if the quantity is reasonable for individual fields
            const MAX_INDIVIDUAL_FIELDS = 50; // Maximum number of individual fields to show

            if (quantity > MAX_INDIVIDUAL_FIELDS) {
                // For large quantities, show a different interface
                note.className = 'alert alert-warning mb-3';
                note.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i> <strong>Large Quantity Detected:</strong> For quantities over ${MAX_INDIVIDUAL_FIELDS}, please use a batch naming convention or upload a CSV file with serial numbers.`;
                serialNumbersFields.appendChild(note);

                // Add a prefix field
                const prefixGroup = document.createElement('div');
                prefixGroup.className = 'mb-3';
                prefixGroup.innerHTML = `
                    <label class="form-label">Serial Number Prefix (Optional)</label>
                    <div class="input-group mb-3">
                        <span class="input-group-text">Prefix</span>
                        <input type="text" class="form-control" id="serial_prefix" name="serial_prefix" placeholder="e.g. LAPTOP-2025-">
                        <span class="input-group-text">+ Number</span>
                    </div>
                    <div class="form-text">Serial numbers will be: PREFIX1, PREFIX2, ..., PREFIX${quantity}</div>
                `;
                serialNumbersFields.appendChild(prefixGroup);

                // Add bulk upload option
                const uploadGroup = document.createElement('div');
                uploadGroup.className = 'mt-4 mb-3';
                uploadGroup.innerHTML = `
                    <label class="form-label">Or Upload Serial Numbers (CSV or TXT)</label>
                    <div class="input-group">
                        <input type="file" class="form-control" id="serial_file" accept=".csv,.txt">
                        <button class="btn btn-outline-secondary" type="button" id="parse_serial_file">Parse</button>
                    </div>
                    <div class="form-text">Upload a file with one serial number per line (${quantity} lines expected)</div>
                `;
                serialNumbersFields.appendChild(uploadGroup);

                // Add a hidden field to store all serial numbers as JSON
                const hiddenField = document.createElement('input');
                hiddenField.type = 'hidden';
                hiddenField.id = 'serial_numbers_json';
                hiddenField.name = 'serial_numbers_json';
                serialNumbersFields.appendChild(hiddenField);

                // Add event listener for file upload parsing
                const parseButton = uploadGroup.querySelector('#parse_serial_file');
                const fileInput = uploadGroup.querySelector('#serial_file');

                parseButton.addEventListener('click', function() {
                    if (fileInput.files.length > 0) {
                        const file = fileInput.files[0];
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const content = e.target.result;
                            const lines = content.split(/\r\n|\n/);

                            if (lines.length < quantity) {
                                alert(`Warning: File contains ${lines.length} lines, but ${quantity} assets are being created.`);
                            }

                            // Store the serial numbers as JSON
                            hiddenField.value = JSON.stringify(lines.slice(0, quantity));

                            // Show a preview
                            const previewDiv = document.createElement('div');
                            previewDiv.className = 'mt-3 small';
                            previewDiv.innerHTML = `<strong>Preview:</strong> ${lines.slice(0, 5).join(', ')}${lines.length > 5 ? '...' : ''}`;

                            // Remove any existing preview
                            const existingPreview = uploadGroup.querySelector('.mt-3.small');
                            if (existingPreview) {
                                uploadGroup.removeChild(existingPreview);
                            }

                            uploadGroup.appendChild(previewDiv);
                        };

                        reader.readAsText(file);
                    } else {
                        alert('Please select a file first.');
                    }
                });

            } else {
                // For reasonable quantities, create individual fields
                // Get the equipment name
                const equipmentName = document.getElementById('asset_name').value || 'equipment';
                note.innerHTML = `<i class="fas fa-info-circle me-2"></i> Please enter individual serial numbers for each ${equipmentName} item.`;
                serialNumbersFields.appendChild(note);

                // Create fields for each item (limited number)
                for (let i = 0; i < quantity; i++) {
                    const row = document.createElement('div');
                    row.className = 'mb-3';

                    const inputGroup = document.createElement('div');
                    inputGroup.className = 'input-group';

                    const inputGroupText = document.createElement('span');
                    inputGroupText.className = 'input-group-text';

                    // Use the equipment name without numbers for cleaner look
                    const equipmentName = document.getElementById('asset_name').value || 'Asset';

                    // For quantities > 10, use just the equipment name without numbers for cleaner appearance
                    if (quantity > 10) {
                        inputGroupText.textContent = equipmentName;
                    } else {
                        // For smaller quantities, keep the numbering for clarity
                        inputGroupText.textContent = `${equipmentName} #${i+1}`;
                    }

                    const input = document.createElement('input');
                    input.type = 'text';
                    input.className = 'form-control';
                    input.id = `serial_number_${i}`;
                    input.name = `serial_number_${i}`;
                    input.placeholder = `Serial number ${i+1}`;
                    // Serial number is not required
                    input.setAttribute('data-asset-index', i); // Add data attribute for tracking

                    // Restore existing value if preserving values
                    if (preserveValues && existingValues[i]) {
                        input.value = existingValues[i];
                    }

                    inputGroup.appendChild(inputGroupText);
                    inputGroup.appendChild(input);
                    row.appendChild(inputGroup);
                    serialNumbersFields.appendChild(row);
                }
            }
        } else {
            // Hide container and enable single field
            serialNumbersContainer.style.display = 'none';
            singleSerialField.disabled = false;
            singleSerialField.parentElement.classList.remove('opacity-50');
        }
    }

    unitCostInput.addEventListener('input', calculateAmount);
    quantityInput.addEventListener('input', function() {
        const quantity = parseInt(this.value) || 0;

        // Warn about very large quantities
        const MAX_ASSETS_WARNING = 1000;
        const MAX_ASSETS_PER_BATCH = 5000;

        if (quantity > MAX_ASSETS_PER_BATCH) {
            // Set to maximum allowed
            this.value = MAX_ASSETS_PER_BATCH;
            alert(`For performance reasons, you can only create up to ${MAX_ASSETS_PER_BATCH} assets at once. The quantity has been adjusted.`);
        } else if (quantity > MAX_ASSETS_WARNING) {
            // Show warning for large quantities
            const confirmLarge = confirm(`You are about to create ${quantity} assets. This may take some time to process. Are you sure you want to continue?`);
            if (!confirmLarge) {
                this.value = MAX_ASSETS_WARNING;
            }
        }

        calculateAmount();
        updateSerialNumberFields();
        // Make sure other fields remain enabled
        enableAllOtherFormFields();
    });

    // Function to show preservation notification
    function showPreservationNotification() {
        // Create a small toast notification
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 1100;';
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');

        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-check-circle me-2"></i>
                    Serial numbers preserved!
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;

        document.body.appendChild(toast);

        // Show the toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 2000
        });
        bsToast.show();

        // Remove the toast element after it's hidden
        toast.addEventListener('hidden.bs.toast', function() {
            if (document.body.contains(toast)) {
                document.body.removeChild(toast);
            }
        });
    }

    // Initialize serial number fields
    updateSerialNumberFields();

    // Handle "Others" selection for supplier
    const supplierSelect = document.getElementById('supplier_id');
    const otherSupplierContainer = document.getElementById('other_supplier_container');

    supplierSelect.addEventListener('change', function() {
        // Assuming the "Others" option has the text "Others"
        const selectedText = this.options[this.selectedIndex].text;
        otherSupplierContainer.style.display = selectedText === 'Others' ? 'block' : 'none';
    });

    // Handle "Other Agencies" selection for source
    const sourceSelect = document.getElementById('source_id');
    const otherSourceContainer = document.getElementById('other_source_container');

    sourceSelect.addEventListener('change', function() {
        // Assuming the "Other Agencies" option has the text "Other Agencies"
        const selectedText = this.options[this.selectedIndex].text;
        otherSourceContainer.style.display = selectedText === 'Other Agencies' ? 'block' : 'none';
    });

    // Bootstrap validation
    const forms = document.querySelectorAll('.needs-validation');

    Array.from(forms).forEach(form => {
        form.addEventListener('submit', event => {
            // Re-check the required attributes before validation
            // This ensures transfer-dependent fields are properly validated
            if (transferLocationSelect.value) {
                assignedToInput.setAttribute('required', 'required');
                localMRInput.setAttribute('required', 'required');
                receiptTypeSelect.setAttribute('required', 'required');
            }

            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();

                // Find the first invalid element and focus it
                const firstInvalid = form.querySelector(':invalid');
                if (firstInvalid) {
                    firstInvalid.focus();
                }
            }

            form.classList.add('was-validated');
        }, false);
    });

    // Handle SKU selection to auto-fill asset name
    const skuSelect = document.getElementById('sku_id');
    const assetNameInput = document.getElementById('asset_name');

    skuSelect.addEventListener('change', function() {
        const selectedOption = this.options[this.selectedIndex];
        if (selectedOption.value) {
            // Extract the name part from "CODE - NAME" format
            const fullText = selectedOption.text;
            const namePart = fullText.split(' - ')[1];
            if (namePart) {
                assetNameInput.value = namePart;

                // Update serial number fields with the new equipment name, preserving existing values
                if (parseInt(quantityInput.value) > 1) {
                    // Check if there are existing serial numbers to preserve
                    const existingInputs = serialNumbersFields.querySelectorAll('input[name^="serial_number_"]');
                    const hasExistingValues = Array.from(existingInputs).some(input => input.value.trim() !== '');

                    updateSerialNumberFields(true); // Preserve existing serial numbers

                    // Show a brief notification if we preserved values
                    if (hasExistingValues) {
                        showPreservationNotification();
                    }
                }
            }
        }
    });

    // Add event listener to update serial number fields when asset name changes
    assetNameInput.addEventListener('input', function() {
        if (parseInt(quantityInput.value) > 1) {
            updateSerialNumberFields(true); // Preserve existing serial numbers
        }
    });

    // Handle form submission validation
    const form = document.getElementById('assetForm');
    const currentLocation = currentLocationSelect.value;

    form.addEventListener('submit', function(event) {
        const transferLocation = transferLocationSelect.value;
        const isLogisticsUse = forLogisticsUseCheckbox.checked;

        // Validate serial numbers for multiple assets
        const quantity = parseInt(quantityInput.value) || 1;
        if (quantity > 1) {
            // Check if we're using individual serial number fields
            if (!document.getElementById('serial_numbers_json') && !document.getElementById('serial_prefix')) {
                // Serial numbers are not required, so we don't need to validate them
                // Just continue with form submission
            }
        }

        // Prevent transferring to the same location
        if (transferLocation !== '' && currentLocation === transferLocation) {
            event.preventDefault();
            alert('Cannot transfer to the same location. Please select a different destination or leave it blank.');
            return;
        }

        // If it's for logistics use, ensure the required fields are filled
        if (isLogisticsUse) {
            let isValid = true;

            // Set status to "In Use" for logistics use
            document.getElementById('status').value = 'In Use';

            // Validate logistics MR number
            if (!logisticsMRNumberInput.value.trim()) {
                event.preventDefault();
                logisticsMRNumberInput.classList.add('is-invalid');
                form.classList.add('was-validated');
                logisticsMRNumberInput.focus();
                isValid = false;
            }

            // Validate logistics staff name
            if (!logisticsStaffInput.value.trim() && isValid) {
                event.preventDefault();
                logisticsStaffInput.classList.add('is-invalid');
                form.classList.add('was-validated');
                logisticsStaffInput.focus();
                isValid = false;
            }

            // Copy values to the main form fields
            if (isValid) {
                assignedToInput.value = logisticsStaffInput.value;
                localMRInput.value = logisticsMRNumberInput.value;

                // Add logistics purpose to remarks if provided
                const remarksField = document.getElementById('remarks');
                if (logisticsPurposeInput.value.trim() && remarksField) {
                    const currentRemarks = remarksField.value.trim();
                    const logisticsPurpose = logisticsPurposeInput.value.trim();

                    if (currentRemarks) {
                        remarksField.value = currentRemarks + "\n\nPurpose: " + logisticsPurpose;
                    } else {
                        remarksField.value = "Purpose: " + logisticsPurpose;
                    }
                }
            }

            if (!isValid) {
                return;
            }
        }

        // If transfer location is selected, make the corresponding fields required
        if (transferLocationSelect.value) {
            assignedToInput.required = true;
            localMRInput.required = true;
            receiptTypeSelect.required = true;

            // Add required indicators to labels
            assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To <span class="text-danger">*</span>';
            localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR <span class="text-danger">*</span>';
            receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type <span class="text-danger">*</span>';

            // Check if these required fields are filled
            let isValid = true;

            if (!assignedToInput.value.trim()) {
                event.preventDefault();
                assignedToInput.classList.add('is-invalid');
                form.classList.add('was-validated');
                assignedToInput.focus();
                isValid = false;
            }

            if (!localMRInput.value.trim() && isValid) {
                event.preventDefault();
                localMRInput.classList.add('is-invalid');
                form.classList.add('was-validated');
                localMRInput.focus();
                isValid = false;
            }

            if (!receiptTypeSelect.value && isValid) {
                event.preventDefault();
                receiptTypeSelect.classList.add('is-invalid');
                form.classList.add('was-validated');
                receiptTypeSelect.focus();
                isValid = false;
            }

            if (!isValid) {
                return;
            }
        } else if (!isLogisticsUse) {
            // Only remove required attributes if not for logistics use
            assignedToInput.required = false;
            localMRInput.required = false;
            receiptTypeSelect.required = false;

            // Remove required indicators from labels
            assignedToInput.parentElement.querySelector('label').innerHTML = 'Assigned To';
            localMRInput.parentElement.querySelector('label').innerHTML = 'Local MR';
            receiptTypeSelect.parentElement.querySelector('label').innerHTML = 'Receipt Type';
        }
    });
});
</script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Handle new SKU creation
    const newCategorySelect = document.getElementById('new_category_id');
    const newSkuCodeInput = document.getElementById('new_sku_code');
    const newSkuNameInput = document.getElementById('new_sku_name');
    const newUnitOfMeasureInput = document.getElementById('new_unit_of_measure');
    const newDescriptionInput = document.getElementById('new_description');
    const saveNewSkuBtn = document.getElementById('saveNewSkuBtn');
    const newSkuForm = document.getElementById('newSkuForm');
    const addSkuModal = document.getElementById('addSkuModal');

    // Initialize tooltips
    if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // Reset form when modal is opened
    addSkuModal.addEventListener('show.bs.modal', function() {
        newSkuForm.reset();
        newSkuCodeInput.value = '';

        // Remove any validation classes
        newSkuForm.classList.remove('was-validated');

        // Clear any previous error messages
        const errorElements = newSkuForm.querySelectorAll('.is-invalid');
        errorElements.forEach(el => el.classList.remove('is-invalid'));

        // Focus on category select after modal is fully shown
        setTimeout(() => {
            newCategorySelect.focus();
        }, 500);
    });

    // Add animation to the SKU code field when it's updated
    function animateSkuCodeUpdate() {
        // Add a highlight effect
        newSkuCodeInput.classList.add('bg-light-success');

        // Add a subtle pulse animation to the input group
        const inputGroup = newSkuCodeInput.closest('.input-group');
        inputGroup.classList.add('pulse-animation');

        // Remove the animation classes after a delay
        setTimeout(() => {
            newSkuCodeInput.classList.remove('bg-light-success');
            inputGroup.classList.remove('pulse-animation');
        }, 1500);
    }

    // Update SKU code when category is selected
    newCategorySelect.addEventListener('change', function() {
        const categoryId = this.value;
        if (categoryId) {
            // Show loading indicator
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'spinner-border spinner-border-sm text-success ms-2';
            loadingIndicator.setAttribute('role', 'status');
            loadingIndicator.innerHTML = '<span class="visually-hidden">Loading...</span>';

            // Add loading indicator next to the form text
            const formText = newSkuCodeInput.closest('.col-md-6').querySelector('.form-text');
            formText.appendChild(loadingIndicator);

            // Disable the input while loading
            newSkuCodeInput.setAttribute('placeholder', 'Generating code...');

            // Make an AJAX call to get the next SKU code for this category
            console.log('Fetching SKU code for category ID:', categoryId);
            fetch(`/choims/modules/admin/get_next_sku_code.php?category_id=${categoryId}`)
                .then(response => {
                    console.log('Response status:', response.status);
                    return response.json();
                })
                .then(data => {
                    console.log('Received data:', data);
                    // Remove loading indicator
                    try {
                        formText.removeChild(loadingIndicator);
                    } catch (e) {
                        console.log('Loading indicator already removed or not found');
                    }

                    if (data.error) {
                        console.error('Error:', data.error);

                        // Show error in a more user-friendly way
                        const errorToast = document.createElement('div');
                        errorToast.className = 'toast align-items-center text-white toast-error border-0 position-fixed bottom-0 end-0 m-3';
                        errorToast.setAttribute('role', 'alert');
                        errorToast.setAttribute('aria-live', 'assertive');
                        errorToast.setAttribute('aria-atomic', 'true');
                        errorToast.innerHTML = `
                            <div class="d-flex">
                                <div class="toast-body">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    Error generating SKU code: ${data.error}
                                </div>
                                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                            </div>
                        `;
                        document.body.appendChild(errorToast);

                        // Show the toast
                        const toast = new bootstrap.Toast(errorToast);
                        toast.show();

                        // Remove the toast after it's hidden
                        errorToast.addEventListener('hidden.bs.toast', function() {
                            document.body.removeChild(errorToast);
                        });
                    } else {
                        // Update the SKU code with animation
                        newSkuCodeInput.value = data.sku_code;
                        animateSkuCodeUpdate();

                        // Focus on the SKU name field
                        setTimeout(() => {
                            newSkuNameInput.focus();
                        }, 300);
                    }
                })
                .catch(error => {
                    // Remove loading indicator
                    try {
                        formText.removeChild(loadingIndicator);
                    } catch (e) {
                        console.log('Loading indicator already removed or not found');
                    }

                    console.error('Error fetching SKU code:', error);

                    // Show error toast
                    const errorToast = document.createElement('div');
                    errorToast.className = 'toast align-items-center text-white toast-error border-0 position-fixed bottom-0 end-0 m-3';
                    errorToast.setAttribute('role', 'alert');
                    errorToast.setAttribute('aria-live', 'assertive');
                    errorToast.setAttribute('aria-atomic', 'true');
                    errorToast.innerHTML = `
                        <div class="d-flex">
                            <div class="toast-body">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                Error fetching SKU code. Please try again.
                            </div>
                            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                    `;
                    document.body.appendChild(errorToast);

                    // Show the toast
                    const toast = new bootstrap.Toast(errorToast);
                    toast.show();

                    // Remove the toast after it's hidden
                    errorToast.addEventListener('hidden.bs.toast', function() {
                        document.body.removeChild(errorToast);
                    });
                });
        } else {
            newSkuCodeInput.value = '';
            newSkuCodeInput.setAttribute('placeholder', '');
        }
    });

    // Add custom validation styles
    function validateForm() {
        let isValid = true;

        // Check each required field
        if (!newCategorySelect.value) {
            newCategorySelect.classList.add('is-invalid');
            isValid = false;
        } else {
            newCategorySelect.classList.remove('is-invalid');
        }

        if (!newSkuCodeInput.value) {
            newSkuCodeInput.classList.add('is-invalid');
            isValid = false;
        } else {
            newSkuCodeInput.classList.remove('is-invalid');
        }

        if (!newSkuNameInput.value) {
            newSkuNameInput.classList.add('is-invalid');
            isValid = false;
        } else {
            newSkuNameInput.classList.remove('is-invalid');
        }

        if (!newUnitOfMeasureInput.value) {
            newUnitOfMeasureInput.classList.add('is-invalid');
            isValid = false;
        } else {
            newUnitOfMeasureInput.classList.remove('is-invalid');
        }

        return isValid;
    }

    // Handle save new SKU button click
    saveNewSkuBtn.addEventListener('click', function() {
        // Add loading state to button
        const originalButtonText = this.innerHTML;
        this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span> Creating...';
        this.disabled = true;

        // Validate form
        if (!validateForm()) {
            // Restore button state
            this.innerHTML = originalButtonText;
            this.disabled = false;

            // Add validation class to form
            newSkuForm.classList.add('was-validated');

            // Focus on the first invalid field
            const firstInvalid = newSkuForm.querySelector('.is-invalid');
            if (firstInvalid) {
                firstInvalid.focus();
            }

            return;
        }

        // Get form values
        const categoryId = newCategorySelect.value;
        const skuCode = newSkuCodeInput.value;
        const skuName = newSkuNameInput.value;
        const unitOfMeasure = newUnitOfMeasureInput.value;
        const description = newDescriptionInput.value;

        // Create form data
        const formData = new FormData();
        formData.append('action', 'add_sku');
        formData.append('category_id', categoryId);
        formData.append('sku_code', skuCode);
        formData.append('sku_name', skuName);
        formData.append('unit_of_measure', unitOfMeasure);
        formData.append('description', description);
        formData.append('item_type', 'Fixed'); // Set item type to Fixed for assets

        // Send AJAX request
        fetch('/choims/modules/assets/add_sku_ajax.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            // Restore button state
            saveNewSkuBtn.innerHTML = originalButtonText;
            saveNewSkuBtn.disabled = false;

            if (data.error) {
                // Show error toast
                const errorToast = document.createElement('div');
                errorToast.className = 'toast align-items-center text-white toast-error border-0 position-fixed bottom-0 end-0 m-3';
                errorToast.setAttribute('role', 'alert');
                errorToast.setAttribute('aria-live', 'assertive');
                errorToast.setAttribute('aria-atomic', 'true');
                errorToast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-exclamation-circle me-2"></i>
                            ${data.error}
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;
                document.body.appendChild(errorToast);

                // Show the toast
                const toast = new bootstrap.Toast(errorToast);
                toast.show();

                // Remove the toast after it's hidden
                errorToast.addEventListener('hidden.bs.toast', function() {
                    document.body.removeChild(errorToast);
                });
            } else {
                // Add new SKU to dropdown and select it
                const skuSelect = document.getElementById('sku_id');
                const newOption = document.createElement('option');
                newOption.value = data.sku_id;
                newOption.text = skuCode + ' - ' + skuName;
                skuSelect.add(newOption);
                skuSelect.value = data.sku_id;

                // Trigger change event to update asset name
                const event = new Event('change');
                skuSelect.dispatchEvent(event);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('addSkuModal'));
                modal.hide();

                // Show success toast
                const successToast = document.createElement('div');
                successToast.className = 'toast align-items-center text-white toast-success border-0 position-fixed bottom-0 end-0 m-3';
                successToast.setAttribute('role', 'alert');
                successToast.setAttribute('aria-live', 'assertive');
                successToast.setAttribute('aria-atomic', 'true');
                successToast.innerHTML = `
                    <div class="d-flex">
                        <div class="toast-body">
                            <i class="fas fa-check-circle me-2"></i>
                            SKU created successfully!
                        </div>
                        <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                    </div>
                `;
                document.body.appendChild(successToast);

                // Show the toast
                const toast = new bootstrap.Toast(successToast);
                toast.show();

                // Remove the toast after it's hidden
                successToast.addEventListener('hidden.bs.toast', function() {
                    document.body.removeChild(successToast);
                });

                // Highlight the SKU dropdown to draw attention to it
                skuSelect.closest('.form-floating').classList.add('highlight-pulse');
                setTimeout(() => {
                    skuSelect.closest('.form-floating').classList.remove('highlight-pulse');
                }, 2000);

                // Reset form
                newSkuForm.reset();
                newSkuCodeInput.value = '';
            }
        })
        .catch(error => {
            // Restore button state
            saveNewSkuBtn.innerHTML = originalButtonText;
            saveNewSkuBtn.disabled = false;

            console.error('Error:', error);

            // Show error toast
            const errorToast = document.createElement('div');
            errorToast.className = 'toast align-items-center text-white toast-error border-0 position-fixed bottom-0 end-0 m-3';
            errorToast.setAttribute('role', 'alert');
            errorToast.setAttribute('aria-live', 'assertive');
            errorToast.setAttribute('aria-atomic', 'true');
            errorToast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        An error occurred. Please try again.
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            `;
            document.body.appendChild(errorToast);

            // Show the toast
            const toast = new bootstrap.Toast(errorToast);
            toast.show();

            // Remove the toast after it's hidden
            errorToast.addEventListener('hidden.bs.toast', function() {
                document.body.removeChild(errorToast);
            });
        });
    });

    // Ensure dates are properly formatted for database storage
    document.querySelector('form').addEventListener('submit', function(e) {
        const purchaseDate = document.getElementById('purchase_date');
        const warrantyExpiry = document.getElementById('warranty_expiry');
        const unitCost = document.getElementById('unit_cost');

        // Process purchase date - make sure it's either valid or empty
        if (purchaseDate.value) {
            try {
                const date = new Date(purchaseDate.value);
                if (date instanceof Date && !isNaN(date)) {
                    // Format as YYYY-MM-DD for database storage
                    const formatted = date.toISOString().split('T')[0];
                    purchaseDate.value = formatted;
                } else {
                    // Invalid date - clear it to prevent errors
                    purchaseDate.value = '';
                }
            } catch (error) {
                purchaseDate.value = '';
            }
        }

        // Process warranty date - make sure it's either valid or empty
        if (warrantyExpiry.value) {
            try {
                const date = new Date(warrantyExpiry.value);
                if (date instanceof Date && !isNaN(date)) {
                    // Format as YYYY-MM-DD for database storage
                    const formatted = date.toISOString().split('T')[0];
                    warrantyExpiry.value = formatted;
                } else {
                    // Invalid date - clear it to prevent errors
                    warrantyExpiry.value = '';
                }
            } catch (error) {
                warrantyExpiry.value = '';
            }
        }

        // Handle numeric fields - ensure they're valid numbers or empty
        if (unitCost.value.trim() === '') {
            // If empty, set to zero
            unitCost.value = '0';
        } else {
            // If not empty, ensure it's a valid number
            const costValue = parseFloat(unitCost.value);
            if (isNaN(costValue)) {
                unitCost.value = '0';
            } else {
                // Format to 2 decimal places
                unitCost.value = costValue.toFixed(2);
            }
        }
    });

    // Format dates for display in "Month Day, Year" format
    const formatDateForDisplay = function(dateInput) {
        if (!dateInput.value) return;

        try {
            const date = new Date(dateInput.value);
            if (date instanceof Date && !isNaN(date)) {
                // Get components
                const month = date.toLocaleString('en-US', { month: 'long' });
                const day = date.getDate();
                const year = date.getFullYear();

                // Format for display (keep the database format in the value)
                const displayDate = `${month} ${day}, ${year}`;

                // Add a data attribute to show the formatted date
                dateInput.setAttribute('data-display-date', displayDate);

                // Create or update a display element next to the date input
                let displayElement = dateInput.nextElementSibling;
                if (!displayElement || !displayElement.classList.contains('date-display')) {
                    displayElement = document.createElement('div');
                    displayElement.classList.add('date-display');
                    dateInput.parentNode.insertBefore(displayElement, dateInput.nextSibling);
                }
                displayElement.textContent = displayDate;
            }
        } catch (error) {
            // Silent fail
        }
    };

    // Initialize date displays
    const purchaseDateInput = document.getElementById('purchase_date');
    const warrantyExpiryInput = document.getElementById('warranty_expiry');

    if (purchaseDateInput) {
        purchaseDateInput.addEventListener('change', function() {
            formatDateForDisplay(this);
        });
        formatDateForDisplay(purchaseDateInput); // Format on load
    }

    if (warrantyExpiryInput) {
        warrantyExpiryInput.addEventListener('change', function() {
            formatDateForDisplay(this);
        });
        formatDateForDisplay(warrantyExpiryInput); // Format on load
    }
});
</script>

<!-- Add SKU Modal -->
<div class="modal fade" id="addSkuModal" tabindex="-1" aria-labelledby="addSkuModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-gradient-green text-white py-2">
                <h5 class="modal-title fw-bold" id="addSkuModalLabel">
                    <i class="fas fa-barcode me-2"></i>Create New SKU
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-3">
                <div class="badge-soft-green d-flex align-items-center mb-2 px-3 py-2">
                    <i class="fas fa-info-circle me-2"></i>
                    <span>Create a new SKU without leaving this page</span>
                </div>

                <form id="newSkuForm" class="needs-validation compact-form" novalidate>
                    <div class="row g-2">
                        <div class="col-md-6">
                            <label for="new_category_id" class="form-label fw-bold">
                                <i class="fas fa-folder me-1 text-success"></i> Category <span class="text-danger">*</span>
                            </label>
                            <select class="form-select shadow-sm" id="new_category_id" name="category_id" required>
                                <option value="">Select Category</option>
                                <?php
                                // Get categories for fixed assets (IT, Office, Medical Equipment)
                                $categoryQuery = "SELECT * FROM categories WHERE category_id IN (1, 2, 3) ORDER BY category_name";
                                $categoryResult = mysqli_query($conn, $categoryQuery);
                                mysqli_data_seek($categoryResult, 0);

                                while ($category = mysqli_fetch_assoc($categoryResult)) {
                                    echo "<option value='" . $category['category_id'] . "'>" . $category['category_name'] . "</option>";
                                }
                                ?>
                            </select>
                            <div class="invalid-feedback">Please select a category</div>
                        </div>

                        <div class="col-md-6">
                            <label for="new_sku_code" class="form-label fw-bold">
                                <i class="fas fa-hashtag me-1 text-success"></i> SKU Code <span class="text-danger">*</span>
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-lock text-success"></i>
                                </span>
                                <input type="text" class="form-control shadow-sm"
                                    id="new_sku_code" name="sku_code" readonly required>
                            </div>
                            <div class="form-text">
                                <i class="fas fa-magic text-success me-1"></i>Auto-generated
                            </div>
                        </div>
                    </div>

                    <div class="row g-2 mt-2">
                        <div class="col-md-6">
                            <label for="new_sku_name" class="form-label fw-bold">
                                <i class="fas fa-tag me-1 text-success"></i> SKU Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control shadow-sm"
                                id="new_sku_name" name="sku_name" required
                                placeholder="Enter item name">
                            <div class="invalid-feedback">Please enter a SKU name</div>
                        </div>

                        <div class="col-md-6">
                            <label for="new_unit_of_measure" class="form-label fw-bold">
                                <i class="fas fa-ruler me-1 text-success"></i> Unit <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control shadow-sm"
                                id="new_unit_of_measure" name="unit_of_measure" required value="Unit"
                                placeholder="e.g. Unit, Piece, Set">
                            <div class="invalid-feedback">Please enter a unit</div>
                        </div>
                    </div>

                    <div class="row g-2 mt-2">
                        <div class="col-12">
                            <label for="new_description" class="form-label fw-bold">
                                <i class="fas fa-align-left me-1 text-success"></i> Description
                            </label>
                            <textarea class="form-control shadow-sm"
                                id="new_description" name="description" rows="2"
                                placeholder="Optional description"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer border-0 p-2">
                <button type="button" class="btn btn-outline-green" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" id="saveNewSkuBtn" class="btn btn-green shadow-sm">
                    <i class="fas fa-save me-1"></i>Create SKU
                </button>
            </div>
        </div>
    </div>
</div>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>