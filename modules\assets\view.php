<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern asset view CSS
echo '<link rel="stylesheet" href="/choims/assets/css/asset-view-modern.css">';

// Ensure user is logged in
requireLogin();

// Get asset ID from URL
$asset_id = isset($_GET['id']) ? sanitizeInput($_GET['id']) : 0;

// Get asset details
$query = "
    SELECT fa.*, sm.sku_code, sm.sku_name, c.category_name, l.location_name, s.source_name, sup.supplier_name,
           u.full_name as created_by_name
    FROM fixed_assets fa
    JOIN sku_master sm ON fa.sku_id = sm.sku_id
    JOIN categories c ON sm.category_id = c.category_id
    JOIN locations l ON fa.current_location_id = l.location_id
    LEFT JOIN sources s ON fa.source_id = s.source_id
    LEFT JOIN suppliers sup ON fa.supplier_id = sup.supplier_id
    LEFT JOIN users u ON fa.created_by = u.user_id
    WHERE fa.asset_id = ?
";

$stmt = mysqli_prepare($conn, $query);
mysqli_stmt_bind_param($stmt, 'i', $asset_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

if (mysqli_num_rows($result) == 0) {
    // Asset not found
    echo '<div class="container-fluid"><div class="alert alert-danger">Asset not found.</div></div>';
    require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
    exit();
}

$asset = mysqli_fetch_assoc($result);

// Check if the asset has an active transfer and get destination location
$activeTransferQuery = "
    SELECT t.*, dst.location_name as destination_location
    FROM transfers t
    JOIN locations dst ON t.destination_location_id = dst.location_id
    WHERE t.asset_id = ?
    AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
    ORDER BY t.transfer_date DESC
    LIMIT 1
";
$activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
mysqli_stmt_execute($activeTransferStmt);
$activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
$activeTransfer = null;
$destinationLocation = null;

if (mysqli_num_rows($activeTransferResult) > 0) {
    $activeTransfer = mysqli_fetch_assoc($activeTransferResult);
    $destinationLocation = $activeTransfer['destination_location'];
}

// Get maintenance records
$maintenanceQuery = "
    SELECT mr.*, u.full_name as technician_name
    FROM maintenance_records mr
    LEFT JOIN users u ON mr.technician_id = u.user_id
    WHERE mr.asset_id = ?
    ORDER BY mr.maintenance_date DESC
";
$maintenanceStmt = mysqli_prepare($conn, $maintenanceQuery);
mysqli_stmt_bind_param($maintenanceStmt, 'i', $asset_id);
mysqli_stmt_execute($maintenanceStmt);
$maintenanceResult = mysqli_stmt_get_result($maintenanceStmt);

// Get MR history records for the widget
$mrHistoryQuery = "
    SELECT
        mh.*,
        u.full_name as created_by_name,
        DATE_FORMAT(mh.start_date, '%d %b %Y %h:%i %p') as formatted_start_date,
        DATE_FORMAT(mh.end_date, '%d %b %Y %h:%i %p') as formatted_end_date
    FROM mr_history mh
    LEFT JOIN users u ON mh.created_by = u.user_id
    WHERE mh.asset_id = ?
    ORDER BY mh.start_date DESC
    LIMIT 5
";
$mrHistoryStmt = mysqli_prepare($conn, $mrHistoryQuery);
mysqli_stmt_bind_param($mrHistoryStmt, 'i', $asset_id);
mysqli_stmt_execute($mrHistoryStmt);
$mrHistoryResult = mysqli_stmt_get_result($mrHistoryStmt);

// Get transfer history
$transferQuery = "
    SELECT t.*,
           src.location_name as source_location,
           dst.location_name as destination_location,
           u1.full_name as initiated_by_name,
           u2.full_name as logistics_approval_by_name,
           u3.full_name as himu_approval_by_name,
           u4.full_name as received_by_name,
           t.created_at
    FROM transfers t
    JOIN locations src ON t.source_location_id = src.location_id
    JOIN locations dst ON t.destination_location_id = dst.location_id
    LEFT JOIN users u1 ON t.initiated_by = u1.user_id
    LEFT JOIN users u2 ON t.logistics_approval_by = u2.user_id
    LEFT JOIN users u3 ON t.himu_approval_by = u3.user_id
    LEFT JOIN users u4 ON t.received_by = u4.user_id
    WHERE t.asset_id = ?
    ORDER BY t.transfer_date DESC
    LIMIT 5
";
$transferStmt = mysqli_prepare($conn, $transferQuery);
mysqli_stmt_bind_param($transferStmt, 'i', $asset_id);
mysqli_stmt_execute($transferStmt);
$transferResult = mysqli_stmt_get_result($transferStmt);

// Get total transfer count for the badge
$transferCountQuery = "
    SELECT COUNT(*) as total_transfers
    FROM transfers
    WHERE asset_id = ?
";
$transferCountStmt = mysqli_prepare($conn, $transferCountQuery);
mysqli_stmt_bind_param($transferCountStmt, 'i', $asset_id);
mysqli_stmt_execute($transferCountStmt);
$transferCountResult = mysqli_stmt_get_result($transferCountStmt);
$transferCount = mysqli_fetch_assoc($transferCountResult)['total_transfers'];
?>

<!-- Modern Asset View Styles are loaded from external CSS file -->

<div class="container-fluid">
    <?php if (isset($_GET['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="fas fa-check-circle me-2"></i> Operation completed successfully!
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($_GET['transfer_received']) || isset($_SESSION['transfer_received'])): ?>
        <?php unset($_SESSION['transfer_received']); ?>
        <div class="alert alert-info alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <div class="d-flex align-items-center">
                <div class="me-3">
                    <i class="fas fa-info-circle fa-2x"></i>
                </div>
                <div>
                    <h5 class="alert-heading mb-1">Transfer Received Successfully!</h5>
                    <p class="mb-0">This asset has been transferred to your location. Would you like to change its status to "In use"?</p>
                </div>
                <div class="ms-auto">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                        <i class="fas fa-check me-1"></i> Change to "In use"
                    </button>
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="asset-header animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8 d-flex align-items-center">
                <div class="asset-icon">
                    <i class="fas fa-desktop"></i>
                </div>
                <div>
                    <h1 class="asset-title"><?php echo htmlspecialchars($asset['asset_name']); ?></h1>
                    <div class="asset-subtitle">
                        <span class="me-3">
                            <i class="fas fa-tag me-1"></i> <?php echo htmlspecialchars($asset['sku_code']); ?>
                        </span>
                        <span class="me-3">
                            <i class="fas fa-map-marker-alt me-1"></i> <?php echo htmlspecialchars($asset['location_name']); ?>
                        </span>
                        <?php
                        $statusClass = '';
                        $statusIcon = '';
                        switch ($asset['status']) {
                            case 'In use':
                                $statusClass = 'bg-primary bg-opacity-10 text-primary';
                                $statusIcon = 'fas fa-check-circle';
                                break;
                            case 'Available':
                                $statusClass = 'bg-success bg-opacity-10 text-success';
                                $statusIcon = 'fas fa-check-circle';
                                break;
                            case 'Under Repair':
                                $statusClass = 'bg-warning bg-opacity-10 text-warning';
                                $statusIcon = 'fas fa-tools';
                                break;
                            case 'Defective':
                                $statusClass = 'bg-danger bg-opacity-10 text-danger';
                                $statusIcon = 'fas fa-exclamation-circle';
                                break;
                            default:
                                $statusClass = 'bg-secondary bg-opacity-10 text-secondary';
                                $statusIcon = 'fas fa-question-circle';
                        }
                        ?>
                        <span class="badge <?php echo $statusClass; ?>">
                            <i class="<?php echo $statusIcon; ?> me-1"></i> <?php echo $asset['status']; ?>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-flex flex-wrap gap-2 justify-content-md-end">
            <?php if (hasRole('Logistics') && $_SESSION['role'] !== 'superadmin'): ?>
                <?php
                // Check if there's an active transfer for this asset
                $activeTransferQuery = "
                    SELECT COUNT(*) as count
                    FROM transfers
                    WHERE asset_id = ?
                    AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
                ";
                $activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
                mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
                mysqli_stmt_execute($activeTransferStmt);
                $activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
                $activeTransfer = mysqli_fetch_assoc($activeTransferResult);
                $hasActiveTransfer = $activeTransfer['count'] > 0;

                // Get detailed asset information including category
                $assetDetailsQuery = "
                    SELECT fa.*, l.location_id, l.location_name, sm.sku_name, c.category_name, c.requires_himu_approval
                    FROM fixed_assets fa
                    JOIN locations l ON fa.current_location_id = l.location_id
                    JOIN sku_master sm ON fa.sku_id = sm.sku_id
                    JOIN categories c ON sm.category_id = c.category_id
                    WHERE fa.asset_id = ?
                ";
                $assetDetailsStmt = mysqli_prepare($conn, $assetDetailsQuery);
                mysqli_stmt_bind_param($assetDetailsStmt, 'i', $asset_id);
                mysqli_stmt_execute($assetDetailsStmt);
                $assetDetailsResult = mysqli_stmt_get_result($assetDetailsStmt);
                $assetDetails = mysqli_fetch_assoc($assetDetailsResult);

                // Check if this is IT equipment or requires HIMU approval
                $isITEquipment = ($assetDetails && ($assetDetails['requires_himu_approval'] == 1 ||
                                strtolower($assetDetails['category_name']) == 'it equipment'));

                // Check if asset is at the user's location
                $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
                $isAtUserLocation = ($userLocationId && $asset['current_location_id'] == $userLocationId);

                // Set default disabled reason
                $disabledReason = "";
                if ($hasActiveTransfer) {
                    $disabledReason = "This asset has an active transfer and cannot be modified.";
                } elseif (!$isAtUserLocation) {
                    $disabledReason = "This asset is at a different location and cannot be modified by you.";
                }

                // Check if user has permission to update this asset
                $hasPermission = false;

                // GodMode, Superadmin can update any asset
                if (hasRole('GodMode', 'Superadmin')) {
                    $hasPermission = true;
                }
                // Logistics can update assets at their location
                else if (hasRole('Logistics') && $isAtUserLocation) {
                    $hasPermission = true;
                }
                // HIMU can update IT equipment anywhere, but other equipment only at their location
                else if (hasRole('HIMU') && ($isITEquipment || $isAtUserLocation)) {
                    $hasPermission = true;
                }
                // Other roles can only update assets at their location
                else if ($isAtUserLocation) {
                    $hasPermission = true;
                }

                // Logistics users can only edit/transfer assets at their own location
                $canModifyAsset = $hasPermission && !$hasActiveTransfer;
                ?>
                <a href="/choims/modules/assets/edit.php?id=<?php echo $asset_id; ?>"
                   class="btn <?php echo $canModifyAsset ? 'btn-primary' : 'btn-light text-muted'; ?> action-btn"
                   <?php echo $canModifyAsset ? '' : 'onclick="return false;"'; ?>
                   <?php echo !$canModifyAsset ? 'data-bs-toggle="tooltip" data-bs-placement="top" title="'.$disabledReason.'"' : ''; ?>>
                    <i class="fas fa-edit"></i> Edit
                </a>
            <?php endif; ?>
            <?php if (hasRole('Logistics') && $_SESSION['role'] !== 'superadmin'): ?>
            <a href="javascript:void(0);"
               onclick="<?php echo $canModifyAsset ? 'validateBeforeTransfer()' : 'return false;'; ?>"
               class="btn <?php echo $canModifyAsset ? 'btn-success' : 'btn-light text-muted'; ?> action-btn"
               <?php echo !$canModifyAsset ? 'data-bs-toggle="tooltip" data-bs-placement="top" title="'.$disabledReason.'"' : ''; ?>>
                <i class="fas fa-exchange-alt"></i> Transfer
            </a>
            <?php endif; ?>
            <?php if (!hasRole('healthcenter') && !hasRole('department')): ?>
            <a href="/choims/modules/reports/asset_history.php?asset_id=<?php echo $asset_id; ?>" class="btn btn-soft-info action-btn">
                <i class="fas fa-history"></i> History
            </a>
            <?php endif; ?>
            <?php if (hasRole('Logistics') || hasRole('Superadmin') || hasRole('Godmode')): ?>
            <a href="/choims/modules/assets/mr_history.php?id=<?php echo $asset_id; ?>" class="btn btn-soft-primary action-btn">
                <i class="fas fa-clipboard-list"></i> MR History
            </a>
            <?php endif; ?>
            <?php if (isset($asset['receipt_type']) && ($asset['receipt_type'] == 'Local MR' || $asset['receipt_type'] == 'D.R' || $asset['receipt_type'] == 'P.T.R' || $asset['receipt_type'] == 'O.R')): ?>
            <button type="button" class="btn btn-soft-success action-btn" onclick="printReceipt()">
                <i class="fas fa-print"></i> Print Receipt
            </button>
            <?php endif; ?>
            <?php
            // Use return URL if provided, otherwise default to list page
            $backUrl = isset($_GET['return_url']) ? urldecode($_GET['return_url']) : '/choims/modules/assets/list.php';
            ?>
            <a href="<?php echo htmlspecialchars($backUrl); ?>" class="btn btn-light action-btn">
                <i class="fas fa-arrow-left"></i> Back
            </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content Area -->
        <div class="col-lg-8">
            <!-- Basic Details Card -->
            <div class="asset-card animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="asset-card-header">
                    <div class="asset-card-title">
                        <i class="fas fa-info-circle"></i> Basic Details
                    </div>
                    <?php
                    // Check if the asset has an active transfer
                    if (!isset($hasActiveTransfer)) {
                        // If not already checked, check for active transfers
                        $activeTransferQuery = "
                            SELECT COUNT(*) as count
                            FROM transfers
                            WHERE asset_id = ?
                            AND status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
                        ";
                        $activeTransferStmt = mysqli_prepare($conn, $activeTransferQuery);
                        mysqli_stmt_bind_param($activeTransferStmt, 'i', $asset_id);
                        mysqli_stmt_execute($activeTransferStmt);
                        $activeTransferResult = mysqli_stmt_get_result($activeTransferStmt);
                        $activeTransfer = mysqli_fetch_assoc($activeTransferResult);
                        $hasActiveTransfer = $activeTransfer['count'] > 0;

                        if ($hasActiveTransfer) {
                            $disabledReason = "This asset has an active transfer and cannot be modified.";
                        }
                    }

                    // Get detailed asset information including category
                    $assetDetailsQuery = "
                        SELECT fa.*, l.location_id, l.location_name, sm.sku_name, c.category_name, c.requires_himu_approval
                        FROM fixed_assets fa
                        JOIN locations l ON fa.current_location_id = l.location_id
                        JOIN sku_master sm ON fa.sku_id = sm.sku_id
                        JOIN categories c ON sm.category_id = c.category_id
                        WHERE fa.asset_id = ?
                    ";
                    $assetDetailsStmt = mysqli_prepare($conn, $assetDetailsQuery);
                    mysqli_stmt_bind_param($assetDetailsStmt, 'i', $asset_id);
                    mysqli_stmt_execute($assetDetailsStmt);
                    $assetDetailsResult = mysqli_stmt_get_result($assetDetailsStmt);
                    $assetDetails = mysqli_fetch_assoc($assetDetailsResult);

                    // Check if this is IT equipment or requires HIMU approval
                    $isITEquipment = ($assetDetails && ($assetDetails['requires_himu_approval'] == 1 ||
                                    strtolower($assetDetails['category_name']) == 'it equipment'));

                    // Get user's location ID
                    $userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;
                    $isAtUserLocation = ($userLocationId && $assetDetails['location_id'] == $userLocationId);

                    // Check if user has permission to update this asset
                    $hasPermission = false;

                    // GodMode, Superadmin can update any asset
                    if (hasRole('GodMode', 'Superadmin')) {
                        $hasPermission = true;
                    }
                    // Logistics can update assets at their location
                    else if (hasRole('Logistics') && $isAtUserLocation) {
                        $hasPermission = true;
                    }
                    // HIMU can update IT equipment anywhere, but other equipment only at their location
                    else if (hasRole('HIMU') && ($isITEquipment || $isAtUserLocation)) {
                        $hasPermission = true;
                    }
                    // Other roles can only update assets at their location
                    else if ($isAtUserLocation) {
                        $hasPermission = true;
                    }

                    // Only show status change button if:
                    // 1. Asset doesn't have an active transfer, AND
                    // 2. User has permission to modify this asset
                    $canChangeStatus = !$hasActiveTransfer && $hasPermission;

                    // Set reason if user can't change status
                    if (!$hasActiveTransfer && !$hasPermission) {
                        $disabledReason = "You don't have permission to update this asset's status.";
                    }

                    // Add debug information for administrators
                    if (hasRole('GodMode', 'Superadmin')) {
                        echo "<div class='small text-muted mb-2'>";
                        echo "<a href='/choims/modules/assets/debug_info.php?id=$asset_id' target='_blank'>Debug Info</a>";
                        echo "</div>";
                    }

                    if ($canChangeStatus):
                    ?>
                    <button type="button" class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#changeStatusModal">
                        <i class="fas fa-sync-alt"></i> Change Status
                    </button>
                    <?php else: ?>
                    <button type="button" class="btn btn-sm btn-outline-secondary disabled"
                            data-bs-toggle="tooltip" data-bs-placement="top"
                            title="<?php echo $disabledReason; ?>">
                        <i class="fas fa-sync-alt"></i> Change Status
                    </button>
                    <?php endif; ?>
                </div>
                <div class="asset-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="asset-field">
                                <div class="field-label">Asset ID</div>
                                <div class="field-value fw-bold">#<?php echo $asset['asset_id']; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Category</div>
                                <div class="field-value"><?php echo $asset['category_name']; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Serial Number</div>
                                <div class="field-value"><?php echo isset($asset['serial_number']) && !empty($asset['serial_number']) ? $asset['serial_number'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Model</div>
                                <div class="field-value"><?php echo isset($asset['model']) && !empty($asset['model']) ? $asset['model'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="asset-field">
                                <div class="field-label">Local MR</div>
                                <div class="field-value"><?php echo isset($asset['local_mr']) && !empty($asset['local_mr']) ? $asset['local_mr'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Current Location</div>
                                <div class="field-value"><?php echo $asset['location_name']; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Assigned To</div>
                                <div class="field-value"><?php echo isset($asset['assigned_to']) && !empty($asset['assigned_to']) ? $asset['assigned_to'] : '<span class="text-muted fst-italic">Not assigned</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Unit/Section</div>
                                <div class="field-value"><?php echo isset($asset['unit_section']) && !empty($asset['unit_section']) ? $asset['unit_section'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Purchase Information Card -->
            <div class="asset-card animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="asset-card-header">
                    <div class="asset-card-title">
                        <i class="fas fa-shopping-cart"></i> Purchase Information
                    </div>
                </div>
                <div class="asset-card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="asset-field">
                                <div class="field-label">Purchase Date</div>
                                <div class="field-value"><?php echo formatDate($asset['purchase_date']); ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Unit Cost</div>
                                <div class="field-value"><?php echo !empty($asset['unit_cost']) ? '₱' . number_format($asset['unit_cost'], 2) : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Receipt Type</div>
                                <div class="field-value">
                                <?php
                                if (isset($asset['receipt_type']) && !empty($asset['receipt_type'])) {
                                    // Display "Local MR" instead of "D.R" for consistency with the edit form
                                    echo $asset['receipt_type'] == 'D.R' ? 'Local MR' : $asset['receipt_type'];
                                } else {
                                    echo '<span class="text-muted fst-italic">Not specified</span>';
                                }
                                ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="asset-field">
                                <div class="field-label">Series Number</div>
                                <div class="field-value"><?php echo isset($asset['series_number']) && !empty($asset['series_number']) ? $asset['series_number'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Supplier</div>
                                <div class="field-value"><?php echo isset($asset['supplier_name']) && !empty($asset['supplier_name']) ? $asset['supplier_name'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                            </div>
                            <div class="asset-field">
                                <div class="field-label">Warranty Expiry</div>
                                <div class="field-value"><?php echo formatDate($asset['warranty_expiry']); ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="asset-field">
                        <div class="field-label">Source</div>
                        <div class="field-value"><?php echo isset($asset['source_name']) && !empty($asset['source_name']) ? $asset['source_name'] : '<span class="text-muted fst-italic">Not specified</span>'; ?></div>
                    </div>
                    <div class="bg-light rounded p-3 mt-3 small">
                        <div class="row text-muted">
                            <div class="col-md-4">
                                <i class="fas fa-user me-1"></i> Created by: <strong><?php echo $asset['created_by_name']; ?></strong>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-calendar-plus me-1"></i> Created on: <?php echo formatDateTime($asset['created_at']); ?>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-calendar-check me-1"></i> Last updated: <?php echo formatDateTime($asset['updated_at']); ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Technical Specifications Card -->
            <div class="asset-card animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="asset-card-header">
                    <div class="asset-card-title">
                        <i class="fas fa-clipboard-list"></i> Technical Specifications
                    </div>
                </div>
                <div class="asset-card-body">
                    <?php if (isset($asset['specifications']) && !empty(trim($asset['specifications']))): ?>
                        <div class="specs-box">
                            <div class="specs-content"><?php echo nl2br(htmlspecialchars($asset['specifications'])); ?></div>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-clipboard fa-2x mb-2"></i>
                            <p>No specifications have been added for this asset.</p>
                        </div>
                    <?php endif; ?>

                    <div class="specs-title mt-4">Additional Notes</div>
                    <?php if (isset($asset['remarks']) && !empty(trim($asset['remarks']))): ?>
                        <div class="specs-box">
                            <div class="specs-content"><?php echo nl2br(htmlspecialchars($asset['remarks'])); ?></div>
                        </div>
                    <?php else: ?>
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-sticky-note fa-2x mb-2"></i>
                            <p>No additional notes have been added for this asset.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar - Transfer History and Maintenance -->
        <div class="col-lg-4">
            <!-- Transfer History -->
            <div class="modern-card mb-4 transfer-history-card">
                <div class="modern-card-header">
                    <div class="d-flex align-items-center">
                        <div class="card-icon-bg transfer">
                            <i class="fas fa-exchange-alt"></i>
                        </div>
                        <h6 class="m-0 ms-2 fw-bold">Transfer History</h6>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="badge bg-soft-primary me-2">
                            <?php echo $transferCount; ?> transfers
                        </div>
                        <?php if($transferCount > 5): ?>
                        <a href="/choims/modules/reports/asset_history.php?asset_id=<?php echo $asset_id; ?>" class="btn btn-soft-primary btn-sm">
                            <i class="fas fa-external-link-alt me-1"></i> View All
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="modern-card-body p-0">
                    <?php if ($transferCount > 0): ?>
                        <div class="transfer-timeline">
                            <?php while ($transfer = mysqli_fetch_assoc($transferResult)): ?>
                                <div class="transfer-item">
                                    <?php
                                    $statusClass = '';
                                    $statusIcon = '';
                                    $statusBgClass = '';
                                    switch ($transfer['status']) {
                                        case 'Pending':
                                            $statusClass = 'warning';
                                            $statusIcon = 'fas fa-clock';
                                            $statusBgClass = 'bg-soft-warning';
                                            break;
                                        case 'Approved by Logistics':
                                        case 'Approved by HIMU':
                                            $statusClass = 'info';
                                            $statusIcon = 'fas fa-thumbs-up';
                                            $statusBgClass = 'bg-soft-info';
                                            break;
                                        case 'Completed':
                                            $statusClass = 'success';
                                            $statusIcon = 'fas fa-check-circle';
                                            $statusBgClass = 'bg-soft-success';
                                            break;
                                        case 'Rejected':
                                            $statusClass = 'danger';
                                            $statusIcon = 'fas fa-times-circle';
                                            $statusBgClass = 'bg-soft-danger';
                                            break;
                                        default:
                                            $statusClass = 'secondary';
                                            $statusIcon = 'fas fa-question-circle';
                                            $statusBgClass = 'bg-soft-secondary';
                                    }
                                    ?>
                                    <div class="transfer-header">
                                        <div class="transfer-date">
                                            <i class="far fa-calendar-alt me-1"></i> <?php echo formatDate($transfer['transfer_date']); ?>
                                        </div>
                                        <div class="transfer-id">
                                            #<?php echo $transfer['transfer_id']; ?>
                                        </div>
                                    </div>
                                    <div class="transfer-content">
                                        <div class="transfer-status <?php echo $statusClass; ?>">
                                            <i class="<?php echo $statusIcon; ?> me-1"></i> <?php echo $transfer['status']; ?>
                                        </div>
                                        <div class="transfer-locations">
                                            <div class="location-item source">
                                                <div class="location-label">From:</div>
                                                <div class="location-name"><?php echo $transfer['source_location']; ?></div>
                                            </div>
                                            <div class="location-arrow">
                                                <i class="fas fa-long-arrow-alt-right"></i>
                                            </div>
                                            <div class="location-item destination">
                                                <div class="location-label">To:</div>
                                                <div class="location-name"><?php echo $transfer['destination_location']; ?></div>
                                            </div>
                                        </div>
                                        <div class="transfer-user">
                                            <i class="fas fa-user me-1"></i> Initiated by <?php echo $transfer['initiated_by_name']; ?>
                                        </div>
                                        <div class="transfer-actions">
                                            <a href="/choims/modules/transfers/view.php?id=<?php echo $transfer['transfer_id']; ?>" class="btn btn-soft-primary btn-sm">
                                                <i class="fas fa-eye me-1"></i> View Details
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-state-icon transfer">
                                <i class="fas fa-exchange-alt"></i>
                            </div>
                            <h6 class="empty-state-title">No Transfer History</h6>
                            <p class="empty-state-description">This asset hasn't been transferred yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- MR History -->
            <?php if (hasRole('Logistics') || hasRole('Superadmin') || hasRole('Godmode')): ?>
            <div class="modern-card mb-4 mr-history-card">
                <div class="modern-card-header">
                    <div class="d-flex align-items-center">
                        <div class="card-icon-bg mr">
                            <i class="fas fa-clipboard-list"></i>
                        </div>
                        <h6 class="m-0 ms-2 fw-bold">MR History</h6>
                    </div>
                    <a href="/choims/modules/assets/mr_history.php?id=<?php echo $asset_id; ?>" class="btn btn-soft-primary btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i> View All
                    </a>
                </div>
                <div class="modern-card-body p-0">
                    <?php
                    $mrCount = mysqli_num_rows($mrHistoryResult);
                    if ($mrCount > 0):
                    ?>
                        <div class="mr-timeline">
                            <?php while ($mr = mysqli_fetch_assoc($mrHistoryResult)): ?>
                                <div class="mr-item">
                                    <div class="mr-header">
                                        <div class="mr-date">
                                            <i class="far fa-calendar-alt me-1"></i> <?php echo $mr['formatted_start_date']; ?>
                                            <?php if ($mr['end_date']): ?>
                                            <span class="text-muted">→</span> <?php echo $mr['formatted_end_date']; ?>
                                            <?php else: ?>
                                            <span class="badge bg-success ms-1">Current</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <div class="mr-content">
                                        <div class="mr-details mb-2">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="detail-label">MR Number:</div>
                                                    <div class="detail-value"><strong><?php echo htmlspecialchars($mr['local_mr']); ?></strong></div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="detail-label">Receipt Type:</div>
                                                    <div class="detail-value"><?php echo htmlspecialchars($mr['receipt_type']); ?></div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="detail-label">Assigned To:</div>
                                                    <div class="detail-value"><?php echo htmlspecialchars($mr['assigned_to']); ?></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mr-footer text-muted small">
                                            <i class="fas fa-user me-1"></i> Recorded by: <?php echo $mr['created_by_name']; ?>
                                            <?php
                                            if ($mr['end_date']) {
                                                // Calculate duration
                                                $start = new DateTime($mr['start_date']);
                                                $end = new DateTime($mr['end_date']);
                                                $interval = $start->diff($end);

                                                echo '<span class="ms-2"><i class="far fa-clock me-1"></i> Duration: ';
                                                if ($interval->y > 0) {
                                                    echo $interval->format('%y years, %m months');
                                                } elseif ($interval->m > 0) {
                                                    echo $interval->format('%m months, %d days');
                                                } elseif ($interval->d > 0) {
                                                    echo $interval->format('%d days');
                                                } else {
                                                    echo $interval->format('%h hours');
                                                }
                                                echo '</span>';
                                            }
                                            ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-state-icon mr">
                                <i class="fas fa-clipboard-list"></i>
                            </div>
                            <h6 class="empty-state-title">No MR History</h6>
                            <p class="empty-state-description">No MR history records found for this asset.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            <?php endif; ?>

            <!-- Maintenance Records -->
            <div class="modern-card mb-4 maintenance-card">
                <div class="modern-card-header">
                    <div class="d-flex align-items-center">
                        <div class="card-icon-bg maintenance">
                            <i class="fas fa-tools"></i>
                        </div>
                        <h6 class="m-0 ms-2 fw-bold">Maintenance Records</h6>
                    </div>
                    <?php
                    // Only GodMode and Superadmin can add maintenance records (removing HIMU)
                    if (hasRole('GodMode') || hasRole('Superadmin')):
                    ?>
                    <button type="button" class="btn btn-soft-primary btn-sm" data-bs-toggle="modal" data-bs-target="#addMaintenanceModal">
                        <i class="fas fa-plus me-1"></i> Add Record
                    </button>
                    <?php endif; ?>
                </div>
                <div class="modern-card-body p-0">
                    <?php if (mysqli_num_rows($maintenanceResult) > 0): ?>
                        <div class="maintenance-list">
                            <?php while ($record = mysqli_fetch_assoc($maintenanceResult)): ?>
                                <?php
                                $statusClass = '';
                                $statusIcon = '';
                                switch ($record['status']) {
                                    case 'Scheduled':
                                        $statusClass = 'info';
                                        $statusIcon = 'fas fa-calendar-check';
                                        break;
                                    case 'In Progress':
                                        $statusClass = 'warning';
                                        $statusIcon = 'fas fa-spinner';
                                        break;
                                    case 'Completed':
                                        $statusClass = 'success';
                                        $statusIcon = 'fas fa-check-circle';
                                        break;
                                    default:
                                        $statusClass = 'secondary';
                                        $statusIcon = 'fas fa-question-circle';
                                }

                                $typeIcon = '';
                                switch ($record['maintenance_type']) {
                                    case 'Preventive':
                                        $typeIcon = 'fas fa-shield-alt';
                                        break;
                                    case 'Corrective':
                                        $typeIcon = 'fas fa-wrench';
                                        break;
                                    case 'Calibration':
                                        $typeIcon = 'fas fa-sliders-h';
                                        break;
                                    default:
                                        $typeIcon = 'fas fa-cog';
                                }
                                ?>
                                <div class="maintenance-item">
                                    <div class="maintenance-icon <?php echo $statusClass; ?>">
                                        <i class="<?php echo $typeIcon; ?>"></i>
                                    </div>
                                    <div class="maintenance-content">
                                        <div class="maintenance-header">
                                            <div class="maintenance-type">
                                                <?php echo $record['maintenance_type']; ?>
                                            </div>
                                            <div class="maintenance-status <?php echo $statusClass; ?>">
                                                <i class="<?php echo $statusIcon; ?> me-1"></i>
                                                <?php echo $record['status']; ?>
                                            </div>
                                        </div>
                                        <div class="maintenance-date">
                                            <i class="far fa-calendar-alt me-1"></i>
                                            <?php echo formatDate($record['maintenance_date']); ?>
                                        </div>
                                        <?php if (!empty($record['performed_by'])): ?>
                                        <div class="maintenance-performer">
                                            <i class="fas fa-user-cog me-1"></i>
                                            <?php echo $record['performed_by']; ?>
                                        </div>
                                        <?php elseif (!empty($record['technician_name'])): ?>
                                        <div class="maintenance-performer">
                                            <i class="fas fa-user-cog me-1"></i>
                                            <?php echo $record['technician_name']; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                    <div class="maintenance-actions">
                                        <button class="btn btn-soft-primary btn-sm view-maintenance-btn" data-record-id="<?php echo $record['record_id']; ?>">
                                            <i class="fas fa-eye me-1"></i> View
                                        </button>
                                    </div>
                                </div>

                                <!-- Maintenance Record Modal will be loaded via AJAX -->
                                    <?php endwhile; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php else: ?>
                        <div class="empty-state">
                            <div class="empty-state-icon maintenance">
                                <i class="fas fa-tools"></i>
                            </div>
                            <h6 class="empty-state-title">No Maintenance Records</h6>
                            <p class="empty-state-description">No maintenance has been performed on this asset yet.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Maintenance Record Modal -->
<?php if (hasRole('GodMode') || hasRole('Superadmin')): ?>
<div class="modal fade" id="addMaintenanceModal" tabindex="-1" aria-labelledby="addMaintenanceModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addMaintenanceModalLabel">Add Maintenance Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/choims/modules/maintenance/add.php" method="post">
                <div class="modal-body">
                    <input type="hidden" name="asset_id" value="<?php echo $asset_id; ?>">

                    <div class="mb-3">
                        <label for="maintenance_type" class="form-label">Maintenance Type *</label>
                        <select class="form-select" id="maintenance_type" name="maintenance_type" required>
                            <option value="">Select Type</option>
                            <option value="Preventive">Preventive</option>
                            <option value="Corrective">Corrective</option>
                            <option value="Calibration">Calibration</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="performed_by" class="form-label">Performed By</label>
                        <input type="text" class="form-control" id="performed_by" name="performed_by">
                    </div>

                    <div class="mb-3">
                        <label for="maintenance_date" class="form-label">Maintenance Date *</label>
                        <input type="date" class="form-control" id="maintenance_date" name="maintenance_date" required>
                    </div>

                    <div class="mb-3">
                        <label for="cost" class="form-label">Cost</label>
                        <input type="number" step="0.01" class="form-control" id="cost" name="cost">
                    </div>

                    <div class="mb-3">
                        <label for="status" class="form-label">Status *</label>
                        <select class="form-select" id="status" name="status" required>
                            <option value="Scheduled">Scheduled</option>
                            <option value="In Progress">In Progress</option>
                            <option value="Completed">Completed</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="next_scheduled_date" class="form-label">Next Scheduled Date</label>
                        <input type="date" class="form-control" id="next_scheduled_date" name="next_scheduled_date">
                    </div>

                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Record</button>
                </div>
            </form>
        </div>
    </div>
</div>
<?php endif; ?>

<!-- Status Change Modal -->
<div class="modal fade" id="changeStatusModal" tabindex="-1" aria-labelledby="changeStatusModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content modern-modal">
            <div class="modal-header">
                <h5 class="modal-title" id="changeStatusModalLabel">
                    <i class="fas fa-sync-alt me-2 text-primary"></i> Change Asset Status
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/choims/modules/assets/update_status.php?id=<?php echo $asset_id; ?>" method="post" id="statusChangeForm">
                <div class="modal-body">
                    <div class="info-banner mb-4">
                        <i class="fas fa-info-circle me-2"></i>
                        Changing the status of an asset will update its availability and condition in the system.
                    </div>

                    <?php if (hasRole('HealthCenter', 'Department') && !hasRole('HIMU', 'GodMode', 'Superadmin')): ?>
                    <div class="info-banner mb-4 bg-warning bg-opacity-10 text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        If this item is defective, please mark it as "Defective". HIMU will be notified and will handle the repair process.
                    </div>
                    <?php endif; ?>

                    <div class="form-group mb-4">
                        <label for="status" class="form-label fw-bold">New Status:</label>
                        <div class="status-options">
                            <div class="row g-2">
                                <div class="col-md-6">
                                    <div class="form-check status-card" data-status="In use">
                                        <input class="form-check-input" type="radio" name="status" id="statusInUse" value="In use" <?php echo ($asset['status'] == 'In use') ? 'checked' : ''; ?> required>
                                        <label class="form-check-label status-card-label" for="statusInUse">
                                            <div class="status-icon bg-primary bg-opacity-10 text-primary">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <div class="status-info">
                                                <span class="status-title">In Use</span>
                                                <span class="status-desc">Asset is currently being used</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check status-card" data-status="Available">
                                        <input class="form-check-input" type="radio" name="status" id="statusAvailable" value="Available" <?php echo ($asset['status'] == 'Available') ? 'checked' : ''; ?> required>
                                        <label class="form-check-label status-card-label" for="statusAvailable">
                                            <div class="status-icon bg-success bg-opacity-10 text-success">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                            <div class="status-info">
                                                <span class="status-title">Available</span>
                                                <span class="status-desc">Asset is available for use</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check status-card" data-status="Under Repair" <?php echo (hasRole('HealthCenter', 'Department') && !hasRole('HIMU', 'GodMode', 'Superadmin')) ? 'style="display:none;"' : ''; ?>>
                                        <input class="form-check-input" type="radio" name="status" id="statusRepair" value="Under Repair" <?php echo ($asset['status'] == 'Under Repair') ? 'checked' : ''; ?> required>
                                        <label class="form-check-label status-card-label" for="statusRepair">
                                            <div class="status-icon bg-warning bg-opacity-10 text-warning">
                                                <i class="fas fa-tools"></i>
                                            </div>
                                            <div class="status-info">
                                                <span class="status-title">Under Repair</span>
                                                <span class="status-desc">Asset is being repaired</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-check status-card" data-status="Defective">
                                        <input class="form-check-input" type="radio" name="status" id="statusDefective" value="Defective" <?php echo ($asset['status'] == 'Defective') ? 'checked' : ''; ?> required>
                                        <label class="form-check-label status-card-label" for="statusDefective">
                                            <div class="status-icon bg-danger bg-opacity-10 text-danger">
                                                <i class="fas fa-exclamation-circle"></i>
                                            </div>
                                            <div class="status-info">
                                                <span class="status-title">Defective</span>
                                                <span class="status-desc">Asset is damaged or not working</span>
                                            </div>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group mb-3">
                        <label for="reason" class="form-label fw-bold">Reason for Status Change:</label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" required
                            placeholder="Please provide a detailed reason for changing the status"></textarea>
                        <div class="form-text text-muted">This information will be recorded in the asset's history.</div>
                    </div>

                    <input type="hidden" name="redirect_url" value="<?php echo $_SERVER['REQUEST_URI']; ?>">
                </div>
                <div class="modal-footer border-0 pt-0">
                    <button type="button" class="btn btn-soft-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-soft-primary">
                        <i class="fas fa-save me-1"></i> Update Status
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Status Change Modal Styles */
.modern-modal {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.modern-modal .modal-header {
    border-bottom: none;
    padding: 1.5rem 1.5rem 0.5rem;
    background-color: white;
}

.modern-modal .modal-body {
    padding: 1rem 1.5rem;
}

.info-banner {
    background-color: rgba(59, 130, 246, 0.08);
    border-radius: 12px;
    padding: 1rem;
    color: #3b82f6;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.info-banner i {
    font-size: 1.2rem;
    margin-right: 0.75rem;
}

.status-options {
    margin-bottom: 1rem;
}

.status-card {
    border: 1px solid var(--gray-200);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all 0.25s ease;
    position: relative;
    background-color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.status-card:hover {
    border-color: var(--primary-light);
    background-color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.status-card input {
    position: absolute;
    top: 1rem;
    right: 1rem;
    margin: 0;
}

.status-card-label {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0;
    cursor: pointer;
    padding-right: 1.5rem;
}

.status-icon {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    flex-shrink: 0;
    transition: all 0.2s ease;
}

.status-info {
    display: flex;
    flex-direction: column;
}

.status-title {
    font-weight: 600;
    font-size: 0.95rem;
    margin-bottom: 0.25rem;
    transition: color 0.2s ease;
}

.status-desc {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.status-card.active {
    border-color: var(--primary);
    background-color: white;
    box-shadow: 0 5px 15px rgba(46, 125, 50, 0.1);
}

/* Make the form check input invisible but still accessible */
.status-card .form-check-input {
    opacity: 0;
    position: absolute;
}

/* Style for selected status card */
.status-card .form-check-input:checked + .status-card-label {
    font-weight: 500;
}

.status-card .form-check-input:checked ~ .status-card-label .status-title {
    color: var(--primary);
}

.status-card .form-check-input:checked ~ .status-card-label .status-icon {
    transform: scale(1.05);
}

.status-card .form-check-input:focus + .status-card-label {
    outline: none;
}

/* Modern soft buttons */
.btn-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary:hover {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
    transform: translateY(-1px);
}

.btn-soft-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-secondary:hover {
    background-color: var(--gray-500);
    color: white;
    box-shadow: 0 4px 10px rgba(107, 114, 128, 0.2);
    transform: translateY(-1px);
}
</style>

<script>
// Add active class to selected status card
document.addEventListener('DOMContentLoaded', function() {
    const statusCards = document.querySelectorAll('.status-card');
    const radioInputs = document.querySelectorAll('.status-card input[type="radio"]');

    // Check if we're coming from a transfer
    const isFromTransfer = <?php echo (isset($_GET['transfer_received']) || isset($_SESSION['transfer_received'])) ? 'true' : 'false'; ?>;

    // If coming from transfer, pre-select "In use" status
    if (isFromTransfer) {
        const inUseRadio = document.getElementById('statusInUse');
        if (inUseRadio) {
            inUseRadio.checked = true;
            inUseRadio.closest('.status-card').classList.add('active');

            // Add a default reason
            const reasonField = document.getElementById('reason');
            if (reasonField && !reasonField.value) {
                reasonField.value = "Asset received from transfer and now in use.";
            }
        }
    } else {
        // Initialize - add active class to selected card
        radioInputs.forEach(input => {
            if (input.checked) {
                input.closest('.status-card').classList.add('active');
            }
        });
    }

    // Add click event to status cards
    statusCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove active class from all cards
            statusCards.forEach(c => c.classList.remove('active'));

            // Add active class to clicked card
            this.classList.add('active');

            // Check the radio input
            const radio = this.querySelector('input[type="radio"]');
            radio.checked = true;
        });
    });

    // Form validation
    const statusForm = document.getElementById('statusChangeForm');
    statusForm.addEventListener('submit', function(event) {
        const reasonField = document.getElementById('reason');
        const selectedStatus = document.querySelector('input[name="status"]:checked');

        if (!selectedStatus) {
            event.preventDefault();
            alert('Please select a status');
            return false;
        }

        if (!reasonField.value.trim()) {
            event.preventDefault();
            reasonField.classList.add('is-invalid');
            return false;
        }

        return true;
    });
});
</script>

<!-- Receipt Template -->
<div id="receiptTemplate" style="display: none;">
    <div class="receipt-container" style="width: 80mm; padding: 10px; font-family: Arial, sans-serif;">
        <div style="text-align: center; margin-bottom: 10px;">
            <h2 style="margin: 0;">CHRISTIAN HOSPITAL OF ILAGAN, INC.</h2>
            <p style="margin: 5px 0;">Ilagan City, Isabela</p>
            <p style="margin: 5px 0;">Tel. No. (*************</p>
            <p style="margin: 5px 0;">Email: <EMAIL></p>
            <p style="margin: 5px 0;">VAT Reg. TIN: 000-000-000-000</p>
            <p style="margin: 5px 0;">Non-VAT</p>
        </div>
        <div style="border-top: 1px dashed #000; border-bottom: 1px dashed #000; padding: 10px 0; margin: 10px 0;">
            <p style="margin: 5px 0;"><strong>Date:</strong> <span id="receipt_date"></span></p>
            <p style="margin: 5px 0;"><strong>Receipt Type:</strong> <span id="receipt_type_display"></span></p>
            <p style="margin: 5px 0;"><strong>Series #:</strong> <span id="receipt_series"></span></p>
            <p style="margin: 5px 0;"><strong>Supplier:</strong> <span id="receipt_supplier"></span></p>
        </div>
        <div style="margin: 10px 0;">
            <p style="margin: 5px 0;"><strong>Asset Name:</strong> <span id="receipt_asset_name"></span></p>
            <p style="margin: 5px 0;"><strong>Serial Number:</strong> <span id="receipt_serial"></span></p>
            <p style="margin: 5px 0;"><strong>Unit Cost:</strong> <span id="receipt_cost"></span></p>
        </div>
        <div style="border-top: 1px dashed #000; padding: 10px 0; margin-top: 10px;">
            <p style="margin: 5px 0; text-align: right;"><strong>Total Amount:</strong> <span id="receipt_total"></span></p>
        </div>
    </div>
</div>

<script>
// Define printReceipt in the global window scope
window.printReceipt = function() {
    console.log('printReceipt function called');
    // Get values from the asset data
    const receiptType = '<?php echo addslashes($asset['receipt_type']); ?>';
    const seriesNumber = '<?php echo addslashes($asset['series_number']); ?>';
    const supplier = '<?php echo isset($asset['supplier_name']) ? addslashes($asset['supplier_name']) : ''; ?>';
    const purchaseDate = '<?php echo isset($asset['purchase_date']) && $asset['purchase_date'] != '0000-00-00' ? date('m/d/Y', strtotime($asset['purchase_date'])) : ''; ?>';
    const unitCost = '<?php echo addslashes($asset['unit_cost']); ?>';
    const assetName = '<?php echo addslashes($asset['asset_name']); ?>';
    const serialNumber = '<?php echo isset($asset['serial_number']) ? addslashes($asset['serial_number']) : ''; ?>';
    const warrantyExpiry = '<?php echo isset($asset['warranty_expiry']) && $asset['warranty_expiry'] != '0000-00-00' ? date('m/d/Y', strtotime($asset['warranty_expiry'])) : ''; ?>';
    const remarks = '<?php echo isset($asset['remarks']) ? addslashes($asset['remarks']) : ''; ?>';
    const model = '<?php echo isset($asset['model']) ? addslashes($asset['model']) : ''; ?>';
    const localMR = '<?php echo isset($asset['local_mr']) ? addslashes($asset['local_mr']) : ''; ?>';
    // Use destination location if there's an active transfer, otherwise use current location
    const transferLocation = '<?php echo $destinationLocation ? addslashes($destinationLocation) : (isset($asset['current_location_id']) ? addslashes($asset['location_name']) : ''); ?>';
    const currentLocation = '<?php echo isset($asset['current_location_id']) ? addslashes($asset['location_name']) : ''; ?>';
    const hasActiveTransfer = <?php echo $destinationLocation ? 'true' : 'false'; ?>;
    const todayDate = new Date().toLocaleDateString('en-US', {month: '2-digit', day: '2-digit', year: 'numeric'});
    const assetId = '<?php echo $asset_id; ?>';
    const assignedTo = '<?php echo isset($asset['assigned_to']) ? addslashes($asset['assigned_to']) : ''; ?>';
    // Create plain text QR content for O.R receipts, URL for other types
    const specifications = '<?php echo isset($asset['specifications']) ? str_replace(array("\n", "\r", "'", "`"), array(" ", " ", "\\'", ""), $asset['specifications']) : ''; ?>';
    const qrCodeUrl = `http://************/choims/modules/assets/receipt.php?id=${assetId}`;

    // Format the receipt based on type
    if (receiptType === 'P.T.R') {
        // Create a new window for the PTR document
        const ptrWindow = window.open('', '_blank', 'width=800,height=600');
        ptrWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Property Transfer Report (PTR)</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; }
                    .document-header { display: flex; align-items: flex-start; justify-content: space-between; margin-bottom: 15px; }
                    .header-text { text-align: left; flex: 1; }
                    .header-text h1 { margin: 0; font-size: 18px; text-transform: uppercase; font-weight: bold; }
                    .header-text h2, .header-text h3 { margin: 0; font-size: 16px; text-transform: uppercase; font-weight: bold; }
                    .header-text p { margin: 0; font-size: 14px; }
                    .logo-container { text-align: center; margin-right: 5px; }
                    .logo-container img { height: 50px; width: 50px; }
                    .logo-container .date-printed { text-align: center; font-size: 12px; margin-top: 5px; }
                    .main-content { padding: 10px; margin-bottom: 20px; }
                    .info-section { margin-bottom: 15px; }
                    .info-row { display: flex; margin-bottom: 5px; }
                    .info-label { width: 120px; font-weight: bold; }
                    .info-data { flex: 1; }
                    .header-separator { border-top: 1px solid #000; margin: 10px 0; }
                    .item-table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                    .item-table th, .item-table td { border: 1px solid #000; padding: 8px; text-align: left; }
                    .item-table th { background-color: #f2f2f2; }
                    .signatures { display: flex; justify-content: space-between; margin-top: 20px; }
                    .signature-block { width: 45%; text-align: center; }
                    .signaturess { display: flex; justify-content: space-between; margin-top: 10px; margin-bottom: 0; }
                    .signature-blockss { width: 45%; text-align: center; }
                    .signature-line { width: 100%; border-top: 1px solid #000; margin-bottom: 0; padding-top: 0; }
                    .signature-block div { margin-top: -2px; line-height: 1.2; }
                    .footer { margin-top: 15px; font-size: 12px; text-align: center; }
                    .print-btn {
                        padding: 8px 16px;
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    .print-btn:hover {
                        background-color: #45a049;
                    }
                    @media print {
                        body {
                            padding: 70px;
                            margin: 0;
                        }
                        button { display: none; }
                    }
                </style>
            </head>
            <body>
                <div class="main-content">
                    <div class="document-header">
                        <div class="logo-container">
                            <img src="/choims/assets/img/prqlogo2.png" alt="Hospital Logo" style="height: 60px; width: auto;">
                        </div>
                        <div class="header-text" style="margin-top: 12px; margin-left: 10px;">
                            <h1 style="margin-bottom: 0;">OFFICE OF THE CITY HEALTH OFFICER</h1>
                            <h5 style="margin-top: 0; font-weight: normal;">PROPERTY TRANSFER REPORT (PTR)</h5>
                        </div>
                    </div>

                    <div class="date-printed" style="font-size: 14px;"><strong>Date:</strong> ${todayDate}</div>

                    <div class="header-separator"></div>

                    <div class="info-section" style="padding-top: 10px; padding-bottom: 5px;">
                        <div class="info-row d-flex justify-content-between">
                            <div style="width: 45%;">
                                <span class="info-label">FROM:</span>
                                <span class="info-data">${hasActiveTransfer ? currentLocation : 'LOGISTICS DIVISION'}</span>
                            </div>
                            <div style="width: 45%;">
                                <span class="info-label">TO:</span>
                                <span class="info-data">${transferLocation || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <table class="item-table">
                        <thead>
                            <tr>
                                <th style="width: 8%;">NO.</th>
                                <th style="width: 35%;">ITEM DESCRIPTION</th>
                                <th style="width: 15%;">TRANSACTION CODE</th>
                                <th style="width: 15%;">TRANSFER DATE</th>
                                <th style="width: 8%;">QTY</th>
                                <th style="width: 19%;">REMARKS</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>1</td>
                                <td>${assetName}${serialNumber ? ' (SN: ' + serialNumber + ')' : ''}</td>
                                <td>${seriesNumber || 'N/A'}</td>
                                <td>${todayDate}</td>
                                <td>1</td>
                                <td>${remarks || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td style="text-align: center; font-style: italic;">-Nothing Follows-</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </tbody>
                    </table>

                    <div class="signaturess" style="margin-top: 20px; margin-bottom: 0;">
                        <div class="signature-block">
                            <div><strong>ISSUED BY:</strong></div>
                        </div>
                        <div class="signature-blockss">
                            <div><strong>RECEIVED BY:</strong></div>
                        </div>
                    </div>

                    <div class="signatures" style="margin-top: 70px; margin-bottom: 10px;">
                        <div class="signature-block">
                            <div class="signature-line"></div>
                            <div style="margin-top: 0;"><small>Signature over Printed Name / Date Issued</small></div>
                            <div><strong>${assignedTo || '___________________'}</strong></div>
                        </div>
                        <div class="signature-block">
                            <div class="signature-line"></div>
                            <div style="margin-top: 1px;"><small>Signature over Printed Name / Date Received</small></div>
                            <div><strong>___________________</strong></div>
                        </div>
                    </div>
                </div>

                <div class="footer" style="margin-top: 20px; text-align: center;">
                    <button class="print-btn" style="padding: 10px 20px; background-color: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2); transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#3d9c40'; this.style.boxShadow='0 6px 10px rgba(76, 175, 80, 0.3)'; this.style.transform='translateY(-2px)';" onmouseout="this.style.backgroundColor='#4CAF50'; this.style.boxShadow='0 4px 6px rgba(76, 175, 80, 0.2)'; this.style.transform='translateY(0)';" onclick="window.print()">
                        <i class="fas fa-print" style="margin-right: 8px;"></i> Print PTR Document
                    </button>
                </div>
            </body>
            </html>
        `);
        ptrWindow.document.close();
    } else if (receiptType === 'O.R') {
        // Create a new window for the O.R document with cache-busting timestamp
        const timestamp = new Date().getTime();
        const orWindow = window.open('', '_blank' + timestamp, 'width=336,height=180');
        orWindow.document.write(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>Official Receipt</title>
                <style>
                    body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 5px;
                        font-size: 8px;
                    }
                    .receipt-container {
                        width: 3.50in;
                        height: 1.50in;
                        padding: 5px;
                        border: 1px solid black;
                        margin: 10px auto;
                        display: flex;
                        flex-direction: column;
                        box-sizing: border-box;
                    }
                    .header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 5px;
                        padding-bottom: 5px;
                        border-bottom: 1px solid #000;
                    }
                    .header-text {
                        text-align: center;
                        flex: 1;
                    }
                    .logo-section {
                        display: flex;
                        align-items: center;
                        margin-right: 10px;
                    }
                    .header-title {
                        font-size: 12px;
                        font-weight: bold;
                        margin: 2px 0;
                        color: #000;
                    }
                    .header-subtitle {
                        font-size: 11px;
                        font-weight: bold;
                        margin: 2px 0;
                        color: #000;
                    }
                    .header-office {
                        font-size: 10px;
                        font-weight: bold;
                        margin: 2px 0;
                    }
                    .logo-row {
                        display: flex;
                        align-items: center;
                    }
                    .logo-row img {
                        height: 25px;
                        margin: 0 3px;
                    }
                    .content-container {
                        display: flex;
                        flex-direction: row;
                        flex: 1;
                        width: 100%;
                        position: relative;
                    }
                    .person-section {
                        width: 30%;
                        border-right: 1px solid #000;
                        padding: 5px;
                    }
                    .person-label {
                        font-size: 8px;
                        font-weight: bold;
                        margin-bottom: 3px;
                    }
                    .person-name {
                        font-size: 10px;
                        font-weight: bold;
                    }
                    .info-section {
                        width: 70%;
                        padding: 5px 65px 5px 10px; /* Right padding for QR code */
                        position: relative;
                    }
                    .unit-label {
                        font-size: 8px;
                        margin: 2px 0;
                        display: block;
                        white-space: normal;
                        overflow-wrap: break-word;
                        word-wrap: break-word;
                        max-width: 100%;
                    }
                    .mr-info {
                        text-align: left;
                        font-size: 8px;
                        font-weight: normal;
                        line-height: 1.3;
                        margin-top: 5px;
                    }
                    .mr-info p {
                        margin: 1px 0;
                    }
                    .receipt-qr {
                        text-align: center;
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        position: absolute;
                        right: 5px;
                        top: 50%;
                        transform: translateY(-50%);
                    }
                    .receipt-qr img {
                        width: 80px;
                        height: 80px;
                        border: 1px solid #ddd;
                        padding: 2px;
                        background-color: white;
                    }
                    @media print {
                        body {
                            padding: 0;
                            margin: 0;
                        }
                        .receipt-container {
                            border: 1px solid black;
                            width: 3.50in;
                            height: 1.50in;
                        }
                        .no-print {
                            display: none !important;
                        }
                        .print-only {
                            display: block;
                        }
                    }
                </style>
            </head>
            <body>
                <div class="receipt-container">
                    <div class="header">
                        <div class="logo-section">
                            <div class="logo-row">
                                <img src="/choims/assets/img/alagangparanaque.jpg" alt="Logo 1">
                                <img src="/choims/assets/img/prqlogo2.png" alt="Logo 2">
                                <img src="/choims/assets/img/prqlogo3.png" alt="Logo 3">
                            </div>
                        </div>
                        <div class="header-text">
                            <div class="header-title">REPUBLIC OF THE PHILIPPINES</div>
                            <div class="header-subtitle">CITY OF PARAÑAQUE</div>
                            <div class="header-office">Parañaque City Health Office</div>
                        </div>
                    </div>

                    <div class="content-container">
                        <div class="person-section">
                            <div class="person-label">PERSON ACCOUNTABLE:</div>
                            <div class="person-name">${assignedTo || 'N/A'}</div>
                        </div>
                        <div class="info-section">
                            <div class="unit-label" id="unit-location" style="${transferLocation && transferLocation.length > 30 ? 'font-size: 7px;' : 'font-size: 7.5px;'}">Unit: ${transferLocation || 'N/A'}</div>
                            <div class="unit-label" style="font-size: 7.5px;">Local MR: ${localMR || 'N/A'}</div>
                            <div class="unit-label" style="font-size: 7.5px;">Date: ${todayDate}</div>
                            <div class="unit-label" style="font-size: 7.5px;">Type: Memorandum Receipt</div>
                            <div class="unit-label" style="font-size: 7.5px;">Serial No: ${seriesNumber || 'N/A'}</div>
                            <div class="unit-label" style="font-size: 7.5px;">Property Tag: ${assetName || 'N/A'}</div>
                        </div>

                        <div class="receipt-qr">
                            <img src="/choims/qr_proxy.php?data=${encodeURIComponent('ASSET: ' + (assetName || 'N/A') + '\nLOCATION: ' + (transferLocation || 'N/A') + '\nMR NUMBER: ' + (localMR || 'N/A'))}&size=100&ecc=H" alt="QR Code" style="width: 70px; height: 70px;" />
                        </div>
                    </div>
                </div>

                <div class="no-print" style="text-align: center; margin-top: 20px;">
                    <button style="padding: 8px 16px; background-color: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2); transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#3d9c40'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.3)'; this.style.transform='translateY(-2px)';" onmouseout="this.style.backgroundColor='#4CAF50'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.2)'; this.style.transform='translateY(0)';" onclick="window.print(); setTimeout(() => {window.focus();}, 1000);">
                        <i class="fas fa-print" style="margin-right: 5px;"></i> Print Sticker
                    </button>
                </div>
            </body>
            </html>
        `);
        orWindow.document.close();
    } else if (receiptType === 'Local MR' || receiptType === 'D.R') {
        // For Local MR, show a dialog with options to print either Local MR or O.R sticker
        const printOptionsDialog = document.createElement('div');
        printOptionsDialog.style.position = 'fixed';
        printOptionsDialog.style.top = '0';
        printOptionsDialog.style.left = '0';
        printOptionsDialog.style.width = '100%';
        printOptionsDialog.style.height = '100%';
        printOptionsDialog.style.backgroundColor = 'rgba(0,0,0,0.6)';
        printOptionsDialog.style.display = 'flex';
        printOptionsDialog.style.justifyContent = 'center';
        printOptionsDialog.style.alignItems = 'center';
        printOptionsDialog.style.zIndex = '9999';

        printOptionsDialog.innerHTML = `
            <div style="background-color: white; padding: 30px; border-radius: 12px; width: 450px; text-align: center; box-shadow: 0 10px 25px rgba(0,0,0,0.2); animation: fadeIn 0.3s ease-out;">
                <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                    <i class="fas fa-print" style="font-size: 24px; color: #4CAF50; margin-right: 10px;"></i>
                    <h3 style="margin: 0; font-size: 22px; color: #333;">Select Print Option</h3>
                </div>

                <p style="color: #666; margin-bottom: 25px; font-size: 15px;">Choose which document to print:</p>

                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 30px 0;">
                    <button id="printLocalMR" style="padding: 15px; background-color: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 15px; font-weight: 500; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.2s ease; box-shadow: 0 4px 6px rgba(76, 175, 80, 0.2);">
                        <i class="fas fa-file-alt" style="font-size: 24px; margin-bottom: 10px;"></i>
                        Print Local MR
                    </button>
                    <button id="printORSticker" style="padding: 15px; background-color: #2196F3; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 15px; font-weight: 500; display: flex; flex-direction: column; align-items: center; justify-content: center; transition: all 0.2s ease; box-shadow: 0 4px 6px rgba(33, 150, 243, 0.2);">
                        <i class="fas fa-tag" style="font-size: 24px; margin-bottom: 10px;"></i>
                        Print Sticker (O.R)
                    </button>
                </div>

                <button id="cancelPrint" style="width: 100%; padding: 12px; background-color: #f1f1f1; color: #666; border: none; border-radius: 8px; cursor: pointer; font-size: 15px; font-weight: 500; transition: all 0.2s ease; margin-top: 10px;">
                    <i class="fas fa-times" style="margin-right: 5px;"></i> Cancel
                </button>
            </div>

            <style>
                @keyframes fadeIn {
                    from { opacity: 0; transform: translateY(-20px); }
                    to { opacity: 1; transform: translateY(0); }
                }

                #printLocalMR:hover {
                    background-color: #3d9c40;
                    box-shadow: 0 6px 10px rgba(76, 175, 80, 0.3);
                    transform: translateY(-2px);
                }

                #printORSticker:hover {
                    background-color: #1a86da;
                    box-shadow: 0 6px 10px rgba(33, 150, 243, 0.3);
                    transform: translateY(-2px);
                }

                #cancelPrint:hover {
                    background-color: #e5e5e5;
                }
            </style>
        `;

        document.body.appendChild(printOptionsDialog);

        // Add event listeners to the buttons
        document.getElementById('printLocalMR').addEventListener('click', function() {
            document.body.removeChild(printOptionsDialog);

            // Create a new window for the Local MR document
            const localMRWindow = window.open('', '_blank', 'width=800,height=600');
            localMRWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Equipment Responsibility Form</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 0; padding: 5px 60px 50px 60px; font-size: 12px; }
                        .document-header { margin-bottom: 10px; }
                        .logo-container { display: flex; align-items: center; justify-content: center; margin-bottom: 10px; }
                        .logo-container img { height: auto; width: auto; margin: 0 5px; }
                        .header-text { text-align: left; }
                        .header-title { text-align: center; font-weight: bold; font-size: 16px; margin-bottom: 5px; }
                        .main-content { padding: 8px; margin-bottom: 15px; }
                        .form-grid { width: 100%; border-collapse: collapse; }
                        .form-grid th, .form-grid td { border: 1px solid #000; padding: 6px; }
                        .form-label { font-weight: bold; width: 150px; }
                        .item-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
                        .item-table th, .item-table td { border: 1px solid #000; padding: 6px; }
                        .item-table th { background-color: #f2f2f2; text-align: center; }
                        .signatures { display: flex; justify-content: space-between; margin-top: 50px; }
                        .signature-block { width: 30%; text-align: center; }
                        .signature-line { border-top: 1px solid #000; margin-bottom: 5px; }
                        .signature-name { font-weight: bold; }
                        .note { font-size: 10px; font-style: italic; margin-top: 10px; }
                        @media print {
                            body {
                                padding: 5px 50px 5px 50px;
                                margin: 0;
                            }
                            button { display: none; }
                            @page {
                                size: auto;
                                margin: 0;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="logo-container">
                        <img src="/choims/assets/img/prqlogo1.jpg" alt="Logo" style="height: 90px; width: 70px; object-fit: contain;">
                        <img src="/choims/assets/img/prqlogo2.png" alt="Logo" style="height: 50px; width: 50px; object-fit: contain;">
                        <img src="/choims/assets/img/prqlogo3.png" alt="Logo" style="height: 50px; width: 70px; object-fit: contain;">
                    </div>

                    <div class="document-header">
                        <div style="text-align: center; margin-top: 5px; margin-bottom: 20px; font-size: 16px;">
                            <div style="font-weight: bold;">Republic of the Philippines</div>
                            <div style="font-weight: bold;">OFFICE OF THE CITY HEALTH OFFICER</div>
                            <div>City of Parañaque</div>
                        </div>
                        <div class="header-title" style="margin-top: 10px;">Equipment Responsibility Form for the Receiving Office</div>
                    </div>

                    <table class="form-grid">
                        <tr>
                            <td class="form-label">Series No:</td>
                            <td>${seriesNumber || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="form-label">Department:</td>
                            <td>${transferLocation || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="form-label">MR/PR No:</td>
                            <td>${localMR || 'N/A'}</td>
                        </tr>
                        <tr>
                            <td class="form-label">Date:</td>
                            <td>${todayDate}</td>
                        </tr>
                        <tr>
                            <td class="form-label">Status:</td>
                            <td>In Use</td>
                        </tr>
                    </table>

                    <div style="margin-top: 15px; margin-bottom: 15px;">
                        This is to certify that he/she received the items in good condition and complete items, as returned by the City Health Office. Below are the details:
                    </div>

                    <table class="item-table">
                        <thead>
                            <tr>
                                <th style="width: 5%;">NO.</th>
                                <th style="width: 40%;">ITEM DESCRIPTION</th>
                                <th style="width: 10%;">SERIAL NO / MODEL</th>
                                <th style="width: 5%;">QTY</th>
                                <th style="width: 15%;">REMARKS</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td style="text-align: center;">1</td>
                                <td>${assetName}</td>
                                <td>${serialNumber || ''}${serialNumber && model ? ' / ' : ''}${model || ''}</td>
                                <td style="text-align: center;">1</td>
                                <td>${remarks || 'N/A'}</td>
                            </tr>
                            <tr>
                                <td style="text-align: center;">2</td>
                                <td style="text-align: center; font-style: italic;">-Nothing Follows-</td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </tbody>
                    </table>

                    <div style="margin-top: 30px;">
                        <p>The receiving party is responsible for keeping the equipment safe and in good condition and is liable for any damage or loss. By signing, the receiving party acknowledges and accepts these responsibilities.</p>
                    </div>

                    <div style="margin-top: 20px;">
                        <table class="form-grid">
                            <tr>
                                <td style="text-decoration: italic;"><strong>Signature of Receiving Personnel:</strong></td>
                            </tr>
                            <tr>
                                <td style="height: 40px; text-align: center;">
                                    <div style="border-top: 1px solid #000; width: 40%; margin: 30px auto 5px;"></div>
                                    <div>${assignedTo}</div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div style="margin-top: 20px;">
                        <table class="form-grid">
                            <tr>
                                <td style="padding: 10px;">
                                    <strong>Prepared by:</strong>
                                    <div style="height: 40px;"></div>
                                    <div style="text-align: center;">
                                        <div>PITZ JERALD B. BELISARIO, RMT</div>
                                        <div>Medical Technologist I - Supply Officer</div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding: 10px;">
                                    <strong>Noted by:</strong>
                                    <div style="height: 40px;"></div>
                                    <div style="text-align: center;">
                                        <div>DR. DARWIN DAVID</div>
                                        <div>Head/Primary Care Service Division</div>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td style="padding: 10px;">
                                    <strong>Approved by:</strong>
                                    <div style="height: 40px;"></div>
                                    <div style="text-align: center;">
                                        <div>DR. RUBEN VER D. BOMBETA</div>
                                        <div>OIC - City Health Office</div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </div>

                    <div class="footer" style="margin-top: 20px; text-align: center;">
                        <button class="print-btn" onclick="window.print()">Print Local MR Document</button>
                    </div>
                </body>
                </html>
            `);
            localMRWindow.document.close();
        });

        document.getElementById('printORSticker').addEventListener('click', function() {
            document.body.removeChild(printOptionsDialog);

            // Create a new window for the O.R sticker with cache-busting timestamp
            const timestamp = new Date().getTime();
            const orWindow = window.open('', '_blank' + timestamp, 'width=336,height=180');
            orWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Official Receipt</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            margin: 0;
                            padding: 5px;
                            font-size: 8px;
                        }
                        .receipt-container {
                            width: 101mm;
                            height: 45mm;
                            padding: 5px;
                            border: 2px solid black;
                            margin: 0 auto;
                            display: flex;
                            flex-direction: column;
                            box-sizing: border-box;
                            overflow: hidden; /* Prevent content from overflowing */
                            position: relative;
                            top: -0px;
                        }
                        .header {
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                            margin-bottom: 0;
                            padding-bottom: 3px;
                            border-bottom: 2px solid #000;
                        }
                        .header-text {
                            text-align: center;
                            flex: 1;
                        }
                        .header-title {
                            font-size: 12px;
                            font-weight: bold;
                            margin: 1px 0;
                            color: #000;
                        }
                        .header-subtitle {
                            font-size: 11px;
                            font-weight: bold;
                            margin: 1px 0;
                            color: #000;
                        }
                        .header-office {
                            font-size: 10px;
                            font-weight: bold;
                            margin: 1px 0;
                        }
                        .logo-section {
                            display: flex;
                            align-items: center;
                            margin-right: 10px;
                        }
                        .logo-row {
                            display: flex;
                            align-items: center;
                        }
                        .logo-row img {
                            height: 28px;
                            margin: 0 2px;
                        }
                        .content-container {
                            display: flex;
                            flex-direction: row;
                            flex: 1;
                            width: 100%;
                            position: relative;
                            border-top: 2px solid #000;
                            height: calc(100% - 40px); /* Adjust based on header height */
                            overflow: hidden;
                        }
                        .person-section {
                            width: 30%;
                            border-right: 2px solid #000;
                            padding: 5px;
                        }
                        .person-label {
                            font-size: 10px;
                            font-weight: bold;
                            margin-bottom: 4px;
                            text-align: center;
                        }
                        .person-name {
                            font-size: 12px;
                            font-weight: bold;
                            text-align: center;
                        }
                        .info-section {
                            width: 70%;
                            padding: 5px 10px 5px 10px; /* Reduced right padding for QR code */
                            position: relative;
                            display: flex;
                            flex-direction: column;
                        }
                        .unit-label {
                            font-size: 11px;
                            margin: 2px 0;
                            display: block;
                            text-align: left;
                            white-space: normal;
                            overflow-wrap: break-word;
                            word-wrap: break-word;
                            max-width: 100%;
                            line-height: 1.2;
                        }
                        .unit-label:first-child {
                            text-align: center;
                            font-weight: bold;
                            font-size: 12px;
                        }
                        .mr-info {
                            text-align: left;
                            font-size: 8px;
                            font-weight: normal;
                            line-height: 1.3;
                            margin-top: 5px;
                        }
                        .mr-info p {
                            margin: 1px 0;
                        }
                        .receipt-qr {
                            text-align: center;
                            display: flex;
                            flex-direction: column;
                            justify-content: center;
                            align-items: center;
                            position: absolute;
                            right: 10px;
                            top: 60%;
                            transform: translateY(-50%);
                        }
                        .receipt-qr img {
                            width: 70px;
                            height: 70px;
                            border: 1px solid #ddd;
                            padding: 2px;
                            background-color: white;
                        }
                        @media print {
                            body {
                                padding: 0;
                                margin: 0;
                            }
                            .receipt-container {
                                border: 2px solid black;
                                width: 101mm;
                                height: 54mm;
                                overflow: hidden;
                            }
                            .content-container {
                                border-top: 2px solid #000;
                            }
                            .person-section {
                                border-right: 2px solid #000;
                            }
                            .header {
                                border-bottom: 2px solid #000;
                            }
                            .no-print {
                                display: none !important;
                            }
                            .print-only {
                                display: block;
                            }
                        }
                    </style>
                </head>
                <body>
                    <div class="receipt-container">
                        <div class="header">
                            <div class="logo-section">
                                <div class="logo-row">
                                    <img src="/choims/assets/img/alagangparanaque.jpg" alt="Logo 1">
                                    <img src="/choims/assets/img/prqlogo2.png" alt="Logo 2">
                                    <img src="/choims/assets/img/prqlogo3.png" alt="Logo 3">
                                </div>
                            </div>
                            <div class="header-text">
                                <div class="header-title">REPUBLIC OF THE PHILIPPINES</div>
                                <div class="header-subtitle">CITY OF PARAÑAQUE</div>
                                <div class="header-office">Parañaque City Health Office</div>
                            </div>
                        </div>

                        <div class="content-container">
                            <div class="person-section">
                                <div class="person-label">PERSON ACCOUNTABLE:</div>
                                <div class="person-name">${assignedTo || 'N/A'}</div>
                            </div>
                            <div class="info-section">
                                <div class="unit-label" id="unit-location" style="${transferLocation && transferLocation.length > 30 ? 'font-size: 9px;' : 'font-size: 10px;'}">Unit: ${transferLocation || 'N/A'}</div>
                                <div class="unit-label" style="font-size: 10px;">Local MR: ${localMR || 'N/A'}</div>
                                <div class="unit-label" style="font-size: 10px;">Date: ${todayDate}</div>
                                <div class="unit-label" style="font-size: 10px;">Type: Memorandum Receipt</div>
                                <div class="unit-label" style="font-size: 10px;">Serial No: ${seriesNumber || 'N/A'}</div>
                                <div class="unit-label" style="font-size: 10px; max-width: 80%;">Property Tag: ${assetName || 'N/A'}</div>

                                <div class="receipt-qr">
                                    <img src="/choims/qr_proxy.php?data=${encodeURIComponent('ASSET: ' + (assetName || 'N/A') + '\nLOCATION: ' + (transferLocation || 'N/A') + '\nMR NUMBER: ' + (localMR || 'N/A'))}&size=100&ecc=H" alt="QR Code" style="width: 70px; height: 70px;" />
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="no-print" style="text-align: center; margin-top: 20px;">
                        <button style="padding: 8px 16px; background-color: #4CAF50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 14px; font-weight: 500; box-shadow: 0 2px 4px rgba(76, 175, 80, 0.2); transition: all 0.2s ease;" onmouseover="this.style.backgroundColor='#3d9c40'; this.style.boxShadow='0 4px 8px rgba(76, 175, 80, 0.3)'; this.style.transform='translateY(-2px)';" onmouseout="this.style.backgroundColor='#4CAF50'; this.style.boxShadow='0 2px 4px rgba(76, 175, 80, 0.2)'; this.style.transform='translateY(0)';" onclick="window.print(); setTimeout(() => {window.focus();}, 1000);">
                            <i class="fas fa-print" style="margin-right: 5px;"></i> Print Sticker
                        </button>
                    </div>
                </body>
                </html>
            `);
            orWindow.document.close();
        });

        document.getElementById('cancelPrint').addEventListener('click', function() {
            document.body.removeChild(printOptionsDialog);
        });
    } else {
        // For other receipt types, use the default template
        document.getElementById('receipt_date').textContent = purchaseDate;
        document.getElementById('receipt_type_display').textContent = receiptType;
        document.getElementById('receipt_series').textContent = seriesNumber;
        document.getElementById('receipt_supplier').textContent = supplier;
        document.getElementById('receipt_asset_name').textContent = assetName;
        document.getElementById('receipt_serial').textContent = serialNumber;
        document.getElementById('receipt_cost').textContent = '₱' + parseFloat(unitCost).toFixed(2);
        document.getElementById('receipt_total').textContent = '₱' + parseFloat(unitCost).toFixed(2);
        receiptHtml = document.getElementById('receiptTemplate').innerHTML;

        // Create a new window for printing
        const printWindow = window.open('', '_blank');
        printWindow.document.write(`
            <html>
                <head>
                    <title>Print Receipt</title>
                    <style>
                        @media print {
                            body { margin: 0; }
                            .receipt-container { width: 80mm; padding: 10px; }
                        }
                    </style>
                </head>
                <body>
                    ${receiptHtml}
                </body>
            </html>
        `);
        printWindow.document.close();
        printWindow.focus();
        printWindow.print();
        printWindow.close();
    }
};


</script>

<!-- Modern UI Styles for Asset View Page -->
<style>
:root {
    --primary: #2E7D32;
    --primary-light: #4CAF50;
    --primary-dark: #1B5E20;
    --primary-bg: rgba(46, 125, 50, 0.08);
    --secondary: #607D8B;
    --success: #10b981;
    --warning: #f59e0b;
    --danger: #ef4444;
    --info: #3b82f6;
    --light: #f8f9fa;
    --dark: #1e293b;
    --white: #ffffff;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --transfer-color: #3b82f6;
    --maintenance-color: #10b981;
}

/* Modern Card Styles */
.modern-card {
    background-color: var(--white);
    border-radius: 16px;
    border: none;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.modern-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.modern-card-header {
    background-color: var(--white);
    border-bottom: 1px solid var(--gray-100);
    padding: 1.25rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modern-card-body {
    padding: 1.5rem;
}

.card-icon-bg {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background-color: var(--primary-bg);
    color: var(--primary);
}

.card-icon-bg.transfer {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--transfer-color);
}

.card-icon-bg.maintenance {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--maintenance-color);
}

/* Transfer History Styles */
.transfer-history-card .modern-card-body {
    padding: 0;
}

.transfer-timeline {
    padding: 0;
}

.transfer-item {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.2s ease;
}

.transfer-item:last-child {
    border-bottom: none;
}

.transfer-item:hover {
    background-color: var(--gray-50);
}

.transfer-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.transfer-date {
    font-size: 0.85rem;
    color: var(--gray-500);
    display: flex;
    align-items: center;
}

.transfer-id {
    font-weight: 600;
    color: var(--transfer-color);
    font-size: 0.9rem;
}

.transfer-content {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.transfer-status {
    display: inline-flex;
    align-items: center;
    padding: 0.35rem 0.75rem;
    border-radius: 50px;
    font-size: 0.8rem;
    font-weight: 500;
}

.transfer-status.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.transfer-status.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.transfer-status.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.transfer-status.danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.transfer-status.secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.transfer-locations {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0.5rem 0;
}

.location-item {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.location-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
}

.location-name {
    font-weight: 500;
    font-size: 0.9rem;
    color: var(--dark);
}

.location-arrow {
    color: var(--gray-400);
    margin: 0 0.5rem;
    margin-top: 0.75rem;
}

.location-item.source .location-name {
    color: var(--gray-700);
}

.location-item.destination .location-name {
    color: var(--transfer-color);
}

.transfer-user {
    font-size: 0.8rem;
    color: var(--gray-500);
}

.transfer-actions {
    margin-top: 0.75rem;
}

/* Maintenance Records Styles */
.maintenance-card .modern-card-body {
    padding: 0;
}

.maintenance-list {
    padding: 0;
}

.maintenance-item {
    display: flex;
    align-items: flex-start;
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid var(--gray-100);
    transition: all 0.2s ease;
}

.maintenance-item:last-child {
    border-bottom: none;
}

.maintenance-item:hover {
    background-color: var(--gray-50);
}

.maintenance-icon {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.1rem;
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.maintenance-icon.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.maintenance-icon.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.maintenance-icon.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.maintenance-icon.danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.maintenance-content {
    flex: 1;
}

.maintenance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.maintenance-type {
    font-weight: 600;
    font-size: 0.95rem;
    color: var(--dark);
}

.maintenance-status {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 500;
}

.maintenance-status.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.maintenance-status.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.maintenance-status.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.maintenance-status.danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.maintenance-date, .maintenance-performer {
    font-size: 0.85rem;
    color: var(--gray-500);
    margin-bottom: 0.25rem;
}

.maintenance-actions {
    margin-left: 1rem;
}

/* Empty State Styles */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1.5rem;
    text-align: center;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    font-size: 1.5rem;
    background-color: var(--gray-100);
    color: var(--gray-500);
}

.empty-state-icon.transfer {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--transfer-color);
}

.empty-state-icon.maintenance {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--maintenance-color);
}

.empty-state-title {
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.empty-state-description {
    color: var(--gray-500);
    max-width: 300px;
    margin: 0 auto;
}

/* Maintenance Modal Styles */
.modal-icon {
    width: 36px;
    height: 36px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
}

.modal-icon.success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.modal-icon.warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.modal-icon.info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.modal-icon.danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.maintenance-status-banner {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-radius: 12px;
    background-color: rgba(107, 114, 128, 0.05);
}

.maintenance-status-banner.success {
    background-color: rgba(16, 185, 129, 0.05);
}

.maintenance-status-banner.warning {
    background-color: rgba(245, 158, 11, 0.05);
}

.maintenance-status-banner.info {
    background-color: rgba(59, 130, 246, 0.05);
}

.maintenance-status-banner.danger {
    background-color: rgba(239, 68, 68, 0.05);
}

.status-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
    font-size: 1.25rem;
}

.maintenance-status-banner.success .status-icon {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.maintenance-status-banner.warning .status-icon {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.maintenance-status-banner.info .status-icon {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
}

.maintenance-status-banner.danger .status-icon {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.status-content {
    flex: 1;
}

.status-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 0.25rem;
}

.maintenance-status-banner.success .status-title {
    color: var(--success);
}

.maintenance-status-banner.warning .status-title {
    color: var(--warning);
}

.maintenance-status-banner.info .status-title {
    color: var(--info);
}

.maintenance-status-banner.danger .status-title {
    color: var(--danger);
}

.status-subtitle {
    font-size: 0.9rem;
    color: var(--gray-500);
}

.status-id {
    font-weight: 600;
    font-size: 0.9rem;
    color: var(--gray-500);
}

/* Info Cards */
.info-card {
    background-color: var(--white);
    border-radius: 12px;
    border: 1px solid var(--gray-200);
    overflow: hidden;
    transition: all 0.2s ease;
    margin-bottom: 1rem;
}

.info-card:hover {
    border-color: var(--primary-light);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.info-card-header {
    background-color: var(--gray-50);
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    border-bottom: 1px solid var(--gray-200);
    font-weight: 600;
    color: var(--dark);
}

.info-card-body {
    padding: 1rem;
}

.info-item {
    display: flex;
    margin-bottom: 0.75rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    width: 120px;
    color: var(--gray-500);
    font-size: 0.9rem;
    padding-top: 0.25rem;
}

.info-value {
    flex: 1;
}

/* User Badge */
.user-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-700);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
}

.user-badge.maintenance {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--maintenance-color);
}

/* Cost Badge */
.cost-badge {
    display: inline-flex;
    align-items: center;
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    font-weight: 600;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
    font-size: 0.9rem;
}

/* Notes Content */
.notes-content {
    background-color: var(--gray-50);
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.9rem;
    color: var(--gray-700);
    line-height: 1.5;
}

/* Soft Buttons */
.btn-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-primary:hover {
    background-color: var(--primary);
    color: white;
    box-shadow: 0 4px 10px rgba(46, 125, 50, 0.2);
    transform: translateY(-1px);
}

.btn-soft-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
    border: none;
    border-radius: 10px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-soft-secondary:hover {
    background-color: var(--gray-500);
    color: white;
    box-shadow: 0 4px 10px rgba(107, 114, 128, 0.2);
    transform: translateY(-1px);
}

.btn-soft-secondary.btn-sm {
    padding: 0.35rem 0.75rem;
    font-size: 0.85rem;
}

/* Soft Badges */
.bg-soft-primary {
    background-color: rgba(46, 125, 50, 0.1);
    color: var(--primary);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-success {
    background-color: rgba(16, 185, 129, 0.1);
    color: var(--success);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-info {
    background-color: rgba(59, 130, 246, 0.1);
    color: var(--info);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-warning {
    background-color: rgba(245, 158, 11, 0.1);
    color: var(--warning);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-danger {
    background-color: rgba(239, 68, 68, 0.1);
    color: var(--danger);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

.bg-soft-secondary {
    background-color: rgba(107, 114, 128, 0.1);
    color: var(--gray-500);
    font-weight: 500;
    padding: 0.35rem 0.75rem;
    border-radius: 6px;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .info-item {
        flex-direction: column;
    }

    .info-label {
        width: 100%;
        margin-bottom: 0.25rem;
    }

    .transfer-locations {
        flex-direction: column;
        align-items: flex-start;
    }

    .location-arrow {
        transform: rotate(90deg);
        margin: 0.5rem 0;
    }

    .maintenance-item {
        flex-direction: column;
    }

    .maintenance-icon {
        margin-bottom: 0.75rem;
    }

    .maintenance-actions {
        margin-left: 0;
        margin-top: 0.75rem;
        width: 100%;
    }

    .maintenance-actions .btn {
        width: 100%;
    }
}

.field-error {
    border: 2px solid #dc3545 !important;
}

/* Fix for modal flickering */
.maintenance-record-modal {
    pointer-events: none;
    z-index: 1050;
}

.maintenance-record-modal.show {
    pointer-events: auto;
}

.maintenance-record-modal .modal-dialog {
    pointer-events: auto;
}

/* Fix for modal backdrop */
.modal-backdrop {
    z-index: 1040;
}

/* Ensure modals don't interfere with each other */
.modal-open .modal {
    overflow-x: hidden;
    overflow-y: auto;
}

/* Maintenance record modal styles */
#maintenanceRecordModal .modal-content {
    border-radius: 16px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

#maintenanceRecordModal .modal-header {
    border-bottom: none;
    padding: 1.5rem 1.5rem 0.5rem;
}

#maintenanceRecordModal .modal-body {
    padding: 1rem 1.5rem;
}

#maintenanceRecordModal .modal-footer {
    border-top: none;
    padding-top: 0;
}
</style>

<script>
// Initialize maintenance record modal with AJAX loading
document.addEventListener('DOMContentLoaded', function() {
    // Define validateBeforeTransfer function in the global scope
    window.validateBeforeTransfer = function() {
        // Check required fields
        let hasLocalMR = "<?php echo !empty($asset['local_mr']) ? 'true' : 'false'; ?>";
        let hasReceiptType = "<?php echo !empty($asset['receipt_type']) ? 'true' : 'false'; ?>";
        let hasAssignedTo = "<?php echo !empty($asset['assigned_to']) ? 'true' : 'false'; ?>";

        // If any required field is missing, redirect to edit page with parameters
        if (hasLocalMR === 'false' || hasReceiptType === 'false' || hasAssignedTo === 'false') {
            let missingFields = [];

            if (hasLocalMR === 'false') missingFields.push('local_mr');
            if (hasReceiptType === 'false') missingFields.push('receipt_type');
            if (hasAssignedTo === 'false') missingFields.push('assigned_to');

            window.location.href = "/choims/modules/assets/edit.php?id=<?php echo $asset_id; ?>&missing=" + missingFields.join(',');
        } else {
            // All required fields are present, proceed to transfer page
            window.location.href = "/choims/modules/transfers/create.php?type=asset&asset_id=<?php echo $asset_id; ?>";
        }
    };

    // Get the single maintenance record modal
    const maintenanceRecordModal = document.getElementById('maintenanceRecordModal');
    let maintenanceModalInstance = null;

    // Initialize the modal once
    if (maintenanceRecordModal) {
        maintenanceModalInstance = new bootstrap.Modal(maintenanceRecordModal);
    }

    // Get all maintenance record view buttons
    const viewButtons = document.querySelectorAll('.view-maintenance-btn');

    // Add click event listeners to each button
    viewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Get the record ID
            const recordId = this.getAttribute('data-record-id');

            // Reset the modal body to show loading indicator
            const modalBody = maintenanceRecordModal.querySelector('.modal-body');
            if (modalBody) {
                modalBody.innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p class="mt-2">Loading record...</p>
                    </div>
                `;
            }

            // Show the modal while content is loading
            maintenanceModalInstance.show();

            // Fetch the maintenance record content via AJAX
            fetch(`/choims/modules/assets/get_maintenance_modal.php?id=${recordId}`)
                .then(response => response.text())
                .then(html => {
                    console.log('Received HTML:', html.substring(0, 100) + '...');

                    // Create a temporary div to parse the HTML
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = html;

                    // Get the modal body from our current modal
                    const modalBody = maintenanceRecordModal.querySelector('.modal-body');

                    // Check if we have a valid response with modal content
                    if (html.includes('maintenance-status-banner')) {
                        // Extract the modal dialog content
                        const newModalDialog = tempDiv.querySelector('.modal-dialog');

                        if (newModalDialog) {
                            // Get the content from the response
                            const newModalContent = newModalDialog.querySelector('.modal-content');

                            if (newModalContent) {
                                // Replace just the modal body content
                                const newModalBody = newModalContent.querySelector('.modal-body');
                                if (newModalBody && modalBody) {
                                    modalBody.innerHTML = newModalBody.innerHTML;
                                }

                                // Replace the modal header content
                                const newModalHeader = newModalContent.querySelector('.modal-header');
                                const currentModalHeader = maintenanceRecordModal.querySelector('.modal-header');
                                if (newModalHeader && currentModalHeader) {
                                    currentModalHeader.innerHTML = newModalHeader.innerHTML;
                                }

                                // Replace the modal footer content
                                const newModalFooter = newModalContent.querySelector('.modal-footer');
                                const currentModalFooter = maintenanceRecordModal.querySelector('.modal-footer');
                                if (newModalFooter && currentModalFooter) {
                                    currentModalFooter.innerHTML = newModalFooter.innerHTML;
                                } else if (newModalFooter) {
                                    // If there's no current footer, add one
                                    const currentModalContent = maintenanceRecordModal.querySelector('.modal-content');
                                    if (currentModalContent) {
                                        currentModalContent.appendChild(newModalFooter.cloneNode(true));
                                    }
                                }
                            }
                        }
                    } else {
                        // If we didn't get valid content, show an error
                        if (modalBody) {
                            modalBody.innerHTML = `
                                <div class="text-center text-danger">
                                    <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                                    <p>Error loading record. Invalid response received.</p>
                                    <small class="text-muted">Please try again or contact support.</small>
                                </div>
                            `;
                        }
                        console.error('Invalid response received:', html);
                    }
                })
                .catch(error => {
                    console.error('Error loading maintenance record:', error);
                    if (modalBody) {
                        modalBody.innerHTML = `
                            <div class="text-center text-danger">
                                <i class="fas fa-exclamation-circle fa-3x mb-3"></i>
                                <p>Error loading record. Please try again.</p>
                            </div>
                        `;
                    }
                });
        });
    });

    // Handle status card selection in the status change modal
    const statusCards = document.querySelectorAll('.status-card');
    const radioInputs = document.querySelectorAll('.status-card input[type="radio"]');

    // Initialize - add active class to selected card
    radioInputs.forEach(input => {
        if (input.checked) {
            input.closest('.status-card').classList.add('active');
        }
    });

    // Add click event to status cards
    statusCards.forEach(card => {
        card.addEventListener('click', function() {
            // Remove active class from all cards
            statusCards.forEach(c => c.classList.remove('active'));

            // Add active class to clicked card
            this.classList.add('active');

            // Check the radio input
            const radio = this.querySelector('input[type="radio"]');
            if (radio) {
                radio.checked = true;
            }
        });
    });

    // Form validation for status change
    const statusForm = document.getElementById('statusChangeForm');
    if (statusForm) {
        statusForm.addEventListener('submit', function(event) {
            const reasonField = document.getElementById('reason');
            const selectedStatus = document.querySelector('input[name="status"]:checked');

            if (!selectedStatus) {
                event.preventDefault();
                alert('Please select a status');
                return false;
            }

            if (!reasonField || !reasonField.value.trim()) {
                event.preventDefault();
                if (reasonField) {
                    reasonField.classList.add('is-invalid');
                }
                alert('Please provide a reason for the status change');
                return false;
            }

            return true;
        });
    }
});
</script>

<!-- Single Maintenance Record Modal Container -->
<div class="modal fade" id="maintenanceRecordModal" tabindex="-1" aria-labelledby="maintenanceRecordModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="maintenanceRecordModalLabel">Maintenance Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center">
                    <div class="spinner-border text-primary" role="status"></div>
                    <p class="mt-2">Loading record...</p>
                </div>
            </div>
            <div class="modal-footer border-0 pt-0">
                <button type="button" class="btn btn-soft-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i> Close
                </button>
            </div>
        </div>
    </div>
</div>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>