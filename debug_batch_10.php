<?php
require_once('includes/config.php');

$batch_id = 10;

echo "<h2>Debug Batch Transfer #$batch_id</h2>";

// Check assets in batch
echo "<h3>Assets in Batch Transfer #$batch_id:</h3>";
$assetsQuery = "SELECT bta.id as bta_id, bta.asset_id, bta.batch_id, fa.asset_name, fa.serial_number, sm.sku_code
               FROM batch_transfer_assets bta
               JOIN fixed_assets fa ON bta.asset_id = fa.asset_id
               JOIN sku_master sm ON fa.sku_id = sm.sku_id
               WHERE bta.batch_id = ?
               ORDER BY bta.asset_id";

$stmt = mysqli_prepare($conn, $assetsQuery);
mysqli_stmt_bind_param($stmt, 'i', $batch_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

echo "<table border='1' style='border-collapse: collapse;'>";
echo "<tr><th>BTA ID</th><th>Asset ID</th><th>Asset Name</th><th>Serial Number</th><th>SKU Code</th><th>Batch ID</th></tr>";
while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . $row['bta_id'] . "</td>";
    echo "<td>" . $row['asset_id'] . "</td>";
    echo "<td>" . $row['asset_name'] . "</td>";
    echo "<td>" . $row['serial_number'] . "</td>";
    echo "<td>" . $row['sku_code'] . "</td>";
    echo "<td>" . $row['batch_id'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for duplicates
echo "<h3>Duplicate Check for Assets:</h3>";
$duplicateAssetsQuery = "SELECT asset_id, COUNT(*) as count 
                        FROM batch_transfer_assets 
                        WHERE batch_id = ? 
                        GROUP BY asset_id 
                        HAVING COUNT(*) > 1";

$stmt3 = mysqli_prepare($conn, $duplicateAssetsQuery);
mysqli_stmt_bind_param($stmt3, 'i', $batch_id);
mysqli_stmt_execute($stmt3);
$result3 = mysqli_stmt_get_result($stmt3);

if (mysqli_num_rows($result3) > 0) {
    echo "<p style='color: red; font-weight: bold;'>DUPLICATE ASSETS FOUND:</p>";
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Asset ID</th><th>Count</th></tr>";
    while ($row = mysqli_fetch_assoc($result3)) {
        echo "<tr style='background-color: #ffcccc;'>";
        echo "<td>" . $row['asset_id'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Show the duplicate entries
    echo "<h4>Duplicate Entry Details:</h4>";
    mysqli_data_seek($result3, 0);
    while ($dup = mysqli_fetch_assoc($result3)) {
        $detailQuery = "SELECT bta.id as bta_id, bta.asset_id, fa.asset_name, fa.serial_number 
                       FROM batch_transfer_assets bta
                       JOIN fixed_assets fa ON bta.asset_id = fa.asset_id
                       WHERE bta.batch_id = ? AND bta.asset_id = ?";
        $detailStmt = mysqli_prepare($conn, $detailQuery);
        mysqli_stmt_bind_param($detailStmt, 'ii', $batch_id, $dup['asset_id']);
        mysqli_stmt_execute($detailStmt);
        $detailResult = mysqli_stmt_get_result($detailStmt);
        
        echo "<p><strong>Asset ID " . $dup['asset_id'] . " appears " . $dup['count'] . " times:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; margin-left: 20px;'>";
        echo "<tr><th>BTA ID</th><th>Asset Name</th><th>Serial Number</th></tr>";
        while ($detail = mysqli_fetch_assoc($detailResult)) {
            echo "<tr>";
            echo "<td>" . $detail['bta_id'] . "</td>";
            echo "<td>" . $detail['asset_name'] . "</td>";
            echo "<td>" . $detail['serial_number'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        mysqli_stmt_close($detailStmt);
    }
} else {
    echo "<p style='color: green; font-weight: bold;'>No duplicate assets found.</p>";
}

// Check inventory items
echo "<h3>Inventory Items in Batch Transfer #$batch_id:</h3>";
$inventoryQuery = "SELECT bti.id as bti_id, bti.inventory_id, bti.quantity, bti.batch_id, sm.sku_name, sm.sku_code
                  FROM batch_transfer_inventory bti
                  JOIN consumable_inventory ci ON bti.inventory_id = ci.inventory_id
                  JOIN sku_master sm ON ci.sku_id = sm.sku_id
                  WHERE bti.batch_id = ?
                  ORDER BY bti.inventory_id";

$stmt2 = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($stmt2, 'i', $batch_id);
mysqli_stmt_execute($stmt2);
$result2 = mysqli_stmt_get_result($stmt2);

if (mysqli_num_rows($result2) > 0) {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>BTI ID</th><th>Inventory ID</th><th>SKU Name</th><th>SKU Code</th><th>Quantity</th><th>Batch ID</th></tr>";
    while ($row = mysqli_fetch_assoc($result2)) {
        echo "<tr>";
        echo "<td>" . $row['bti_id'] . "</td>";
        echo "<td>" . $row['inventory_id'] . "</td>";
        echo "<td>" . $row['sku_name'] . "</td>";
        echo "<td>" . $row['sku_code'] . "</td>";
        echo "<td>" . $row['quantity'] . "</td>";
        echo "<td>" . $row['batch_id'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p>No inventory items found in this batch transfer.</p>";
}

echo "<br><br>";
echo "<a href='modules/transfers/batch/view.php?id=$batch_id' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px;'>View Batch Transfer #$batch_id</a>";
?>
