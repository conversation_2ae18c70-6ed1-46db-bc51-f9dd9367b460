<?php
require_once('includes/config.php');

$batch_id = 8;

echo "<h2>Debug Batch Transfer #$batch_id</h2>";

// Check assets in batch
echo "<h3>Assets in Batch:</h3>";
$assetsQuery = "SELECT bta.*, fa.asset_name, fa.asset_id 
               FROM batch_transfer_assets bta
               JOIN fixed_assets fa ON bta.asset_id = fa.asset_id
               WHERE bta.batch_id = ?";

$stmt = mysqli_prepare($conn, $assetsQuery);
mysqli_stmt_bind_param($stmt, 'i', $batch_id);
mysqli_stmt_execute($stmt);
$result = mysqli_stmt_get_result($stmt);

echo "<table border='1'>";
echo "<tr><th>BTA ID</th><th>Asset ID</th><th>Asset Name</th><th>Batch ID</th></tr>";
while ($row = mysqli_fetch_assoc($result)) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['asset_id'] . "</td>";
    echo "<td>" . $row['asset_name'] . "</td>";
    echo "<td>" . $row['batch_id'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check inventory in batch
echo "<h3>Inventory in Batch:</h3>";
$inventoryQuery = "SELECT bti.*, ci.inventory_id, sm.sku_name
                  FROM batch_transfer_inventory bti
                  JOIN consumable_inventory ci ON bti.inventory_id = ci.inventory_id
                  JOIN sku_master sm ON ci.sku_id = sm.sku_id
                  WHERE bti.batch_id = ?";

$stmt2 = mysqli_prepare($conn, $inventoryQuery);
mysqli_stmt_bind_param($stmt2, 'i', $batch_id);
mysqli_stmt_execute($stmt2);
$result2 = mysqli_stmt_get_result($stmt2);

echo "<table border='1'>";
echo "<tr><th>BTI ID</th><th>Inventory ID</th><th>SKU Name</th><th>Quantity</th><th>Batch ID</th></tr>";
while ($row = mysqli_fetch_assoc($result2)) {
    echo "<tr>";
    echo "<td>" . $row['id'] . "</td>";
    echo "<td>" . $row['inventory_id'] . "</td>";
    echo "<td>" . $row['sku_name'] . "</td>";
    echo "<td>" . $row['quantity'] . "</td>";
    echo "<td>" . $row['batch_id'] . "</td>";
    echo "</tr>";
}
echo "</table>";

// Check for duplicates
echo "<h3>Duplicate Check:</h3>";
$duplicateAssetsQuery = "SELECT asset_id, COUNT(*) as count 
                        FROM batch_transfer_assets 
                        WHERE batch_id = ? 
                        GROUP BY asset_id 
                        HAVING COUNT(*) > 1";

$stmt3 = mysqli_prepare($conn, $duplicateAssetsQuery);
mysqli_stmt_bind_param($stmt3, 'i', $batch_id);
mysqli_stmt_execute($stmt3);
$result3 = mysqli_stmt_get_result($stmt3);

if (mysqli_num_rows($result3) > 0) {
    echo "<p style='color: red;'>DUPLICATE ASSETS FOUND:</p>";
    echo "<table border='1'>";
    echo "<tr><th>Asset ID</th><th>Count</th></tr>";
    while ($row = mysqli_fetch_assoc($result3)) {
        echo "<tr>";
        echo "<td>" . $row['asset_id'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>No duplicate assets found.</p>";
}

$duplicateInventoryQuery = "SELECT inventory_id, COUNT(*) as count 
                           FROM batch_transfer_inventory 
                           WHERE batch_id = ? 
                           GROUP BY inventory_id 
                           HAVING COUNT(*) > 1";

$stmt4 = mysqli_prepare($conn, $duplicateInventoryQuery);
mysqli_stmt_bind_param($stmt4, 'i', $batch_id);
mysqli_stmt_execute($stmt4);
$result4 = mysqli_stmt_get_result($stmt4);

if (mysqli_num_rows($result4) > 0) {
    echo "<p style='color: red;'>DUPLICATE INVENTORY FOUND:</p>";
    echo "<table border='1'>";
    echo "<tr><th>Inventory ID</th><th>Count</th></tr>";
    while ($row = mysqli_fetch_assoc($result4)) {
        echo "<tr>";
        echo "<td>" . $row['inventory_id'] . "</td>";
        echo "<td>" . $row['count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} else {
    echo "<p style='color: green;'>No duplicate inventory found.</p>";
}
?>
