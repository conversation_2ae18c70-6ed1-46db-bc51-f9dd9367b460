<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/header.php');

// Add the modern batch transfer select CSS
echo '<link rel="stylesheet" href="/choims/assets/css/batch-transfer-select-modern.css">';

// Ensure user is logged in
requireLogin();

// Prevent superadmin from accessing this page (direct URL protection)
if (strtolower($_SESSION['role']) === 'superadmin') {
    // Redirect to list view instead
    header("Location: /choims/modules/transfers/list.php");
    exit();
}

// Only relevant roles can access this page
if (!hasRole('GodMode', 'Superadmin', 'Logistics', 'Department', 'HealthCenter', 'HIMU')) {
    header('Location: /choims/index.php');
    exit();
}

// Check if there are error messages in URL or session
$error_message = '';
if (isset($_GET['error'])) {
    $error_message = sanitizeInput($_GET['error']);
} elseif (isset($_SESSION['batch_transfer_error'])) {
    $error_message = $_SESSION['batch_transfer_error'];
    unset($_SESSION['batch_transfer_error']);
}

// Get the item type from URL parameter or POST data
$itemType = isset($_GET['type']) && in_array($_GET['type'], ['asset', 'inventory']) ? $_GET['type'] : 'asset';
if (isset($_POST['type']) && in_array($_POST['type'], ['asset', 'inventory'])) {
    $itemType = $_POST['type'];
}

// Get preselected items from POST data
$preselectedInventory = isset($_POST['preselected_inventory']) ? $_POST['preselected_inventory'] : [];
$preselectedAssets = isset($_POST['preselected_assets']) ? $_POST['preselected_assets'] : [];

// Get filter parameters
$categoryFilter = isset($_GET['category']) && !empty($_GET['category']) ? (int)$_GET['category'] : null;
$locationFilter = isset($_GET['location']) && !empty($_GET['location']) ? (int)$_GET['location'] : null;

// Get user's location for default source
$userLocationId = isset($_SESSION['location_id']) ? $_SESSION['location_id'] : null;

// Get locations for filtering
$locationsQuery = "SELECT * FROM locations ORDER BY location_name";
$locationsResult = mysqli_query($conn, $locationsQuery);
$locations = [];
while ($location = mysqli_fetch_assoc($locationsResult)) {
    $locations[$location['location_id']] = $location['location_name'];
}

// Get categories for filtering
$categoriesQuery = "SELECT * FROM categories ORDER BY category_name";
$categoriesResult = mysqli_query($conn, $categoriesQuery);

// Initialize the query based on the item type
if ($itemType == 'asset') {
    // Query for assets
    $query = "SELECT a.asset_id, a.asset_name, a.serial_number, a.status, a.current_location_id,
                     s.sku_code, s.sku_name, c.category_name, l.location_name
              FROM fixed_assets a
              JOIN sku_master s ON a.sku_id = s.sku_id
              JOIN categories c ON s.category_id = c.category_id
              JOIN locations l ON a.current_location_id = l.location_id
              WHERE a.is_active = 1
              AND (a.is_deleted = 0 OR a.is_deleted IS NULL)
              AND NOT EXISTS (
                  SELECT 1 FROM transfers t
                  WHERE t.asset_id = a.asset_id
                  AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
              )
              AND NOT EXISTS (
                  SELECT 1 FROM batch_transfer_assets bta
                  JOIN batch_transfers bt ON bta.batch_id = bt.batch_id
                  WHERE bta.asset_id = a.asset_id
                  AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
              )";

    // Always filter by user's location unless user is GodMode
    if (!hasRole('GodMode') && !empty($userLocationId)) {
        $query .= " AND a.current_location_id = " . $userLocationId;
    }

    // Apply category filter if selected
    if (!is_null($categoryFilter)) {
        $query .= " AND s.category_id = " . $categoryFilter;
    }

    // Apply location filter if selected (for GodMode users)
    if (hasRole('GodMode') && !is_null($locationFilter)) {
        $query .= " AND a.current_location_id = " . $locationFilter;
    }

    // Add order by clause
    $query .= " ORDER BY a.asset_name";

    // Execute the query
    $result = mysqli_query($conn, $query);

} else {
    // Query for inventory items
    $query = "SELECT i.inventory_id, i.current_quantity, i.status, i.location_id,
                     s.sku_code, s.sku_name, s.unit_of_measure, c.category_name, l.location_name
              FROM consumable_inventory i
              JOIN sku_master s ON i.sku_id = s.sku_id
              JOIN categories c ON s.category_id = c.category_id
              JOIN locations l ON i.location_id = l.location_id
              WHERE i.current_quantity > 0
              AND (i.is_deleted = 0 OR i.is_deleted IS NULL)
              AND NOT EXISTS (
                  SELECT 1 FROM transfers t
                  WHERE t.inventory_id = i.inventory_id
                  AND t.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
              )
              AND NOT EXISTS (
                  SELECT 1 FROM batch_transfer_inventory bti
                  JOIN batch_transfers bt ON bti.batch_id = bt.batch_id
                  WHERE bti.inventory_id = i.inventory_id
                  AND bt.status IN ('Pending', 'Approved by Logistics', 'Approved by HIMU')
              )";

    // Always filter by user's location unless user is GodMode
    if (!hasRole('GodMode') && !empty($userLocationId)) {
        $query .= " AND i.location_id = " . $userLocationId;
    }

    // Apply category filter if selected
    if (!is_null($categoryFilter)) {
        $query .= " AND s.category_id = " . $categoryFilter;
    }

    // Apply location filter if selected (for GodMode users)
    if (hasRole('GodMode') && !is_null($locationFilter)) {
        $query .= " AND i.location_id = " . $locationFilter;
    }

    // Add order by clause
    $query .= " ORDER BY s.sku_name";

    // Execute the query
    $result = mysqli_query($conn, $query);
}
?>

<div class="container-fluid">
    <!-- Page heading -->
    <div class="page-header mb-4 animate__animated animate__fadeIn">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="page-title"><i class="fas fa-exchange-alt"></i> Batch Transfer</h1>
                <p class="page-subtitle">Select multiple items to transfer at once</p>
            </div>
            <div class="col-md-4 text-md-end">
                <a href="/choims/modules/transfers/list.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Transfers
                </a>
            </div>
        </div>
    </div>

    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger d-flex align-items-start animate__animated animate__fadeInUp" role="alert">
            <div class="alert-icon mt-1">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="ms-3">
                <?php
                // Check if it's the missing MR receipt error
                if (strpos($error_message, 'missing required MR receipt information') !== false) {
                    // Extract asset IDs from the error message
                    preg_match_all('/\(ID: (\d+)\)/', $error_message, $matches);
                    $assetIds = $matches[1];

                    // Message explaining the issue
                    echo "<p class='fw-medium mb-2'>Some assets are missing required MR receipt information.</p>";
                    echo "<p class='mb-2'>Please update the following assets before transferring:</p>";
                    echo "<ul class='mb-2'>";

                    // Extract asset names and IDs using regex
                    preg_match_all('/([^,]+) \(ID: (\d+)\)/', $error_message, $matches, PREG_SET_ORDER);

                    foreach ($matches as $match) {
                        $assetName = $match[1];
                        $assetId = $match[2];
                        echo "<li><a href='/choims/modules/assets/edit.php?id={$assetId}' target='_blank' class='text-danger'>{$assetName}</a></li>";
                    }

                    echo "</ul>";
                    echo "<p class='mb-0'>After updating these assets, you can come back to this page to complete the batch transfer.</p>";
                } else {
                    // Display normal error message
                    echo $error_message;
                }
                ?>
            </div>
            <button type="button" class="btn-close ms-auto" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card animate__animated animate__fadeInUp animate__faster">
        <div class="card-header">
            <div class="card-header-title">
                <i class="fas fa-clipboard-list"></i>
                Select Items to Transfer
            </div>
        </div>
        <div class="card-body">
            <div class="alert alert-info d-flex align-items-center" role="alert">
                <div class="alert-icon">
                    <i class="fas fa-info-circle"></i>
                </div>
                <div>
                    Items that already have an active transfer in progress are not shown in this list.
                </div>
            </div>
            <!-- Item Type Selection Tabs -->
            <ul class="nav nav-tabs mb-4">
                <li class="nav-item">
                    <a class="nav-link <?php echo $itemType == 'asset' ? 'active' : ''; ?>" href="#" data-tab="asset" id="assetTab">
                        Fixed Assets <span class="badge bg-primary ms-1" id="assetCount" style="display: none;">0</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link <?php echo $itemType == 'inventory' ? 'active' : ''; ?>" href="#" data-tab="inventory" id="inventoryTab">
                        Consumable Inventory <span class="badge bg-info ms-1" id="inventoryCount" style="display: none;">0</span>
                    </a>
                </li>
            </ul>

            <!-- Filter Form -->
            <div class="filter-section animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.1s;">
                <div class="filter-header mb-3">
                    <div class="filter-title">
                        <i class="fas fa-filter me-2"></i> Filter Items
                    </div>
                    <?php if (!empty($categoryFilter) || !empty($locationFilter)): ?>
                    <a href="?type=<?php echo $itemType; ?>" class="clear-filters">
                        <i class="fas fa-times-circle me-1"></i> Clear Filters
                    </a>
                    <?php endif; ?>
                </div>

                <form id="filterForm" class="mb-3">
                    <input type="hidden" name="type" value="<?php echo $itemType; ?>">

                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="filterCategory" class="form-label small fw-medium">Filter by Category</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-tag text-primary"></i></span>
                                <select class="form-select rounded-end-3 border-start-0" id="filterCategory" name="category">
                                    <option value="">All Categories</option>
                                    <?php mysqli_data_seek($categoriesResult, 0); ?>
                                    <?php while ($category = mysqli_fetch_assoc($categoriesResult)): ?>
                                        <option value="<?php echo $category['category_id']; ?>" <?php echo $categoryFilter == $category['category_id'] ? 'selected' : ''; ?>>
                                            <?php echo $category['category_name']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                        </div>

                        <?php if (hasRole('GodMode')): ?>
                        <div class="col-md-4">
                            <label for="filterLocation" class="form-label small fw-medium">Filter by Location</label>
                            <div class="input-group">
                                <span class="input-group-text bg-light rounded-start-3 border-end-0"><i class="fas fa-map-marker-alt text-primary"></i></span>
                                <select class="form-select rounded-end-3 border-start-0" id="filterLocation" name="location">
                                    <option value="">All Locations</option>
                                    <?php foreach ($locations as $id => $name): ?>
                                        <option value="<?php echo $id; ?>" <?php echo $locationFilter == $id ? 'selected' : ''; ?>>
                                            <?php echo $name; ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i> Apply Filters
                            </button>

                            <?php if (!empty($categoryFilter) || !empty($locationFilter)): ?>
                            <a href="?type=<?php echo $itemType; ?>" class="btn btn-outline-secondary ms-2">
                                <i class="fas fa-times me-2"></i> Reset
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if (!empty($categoryFilter) || !empty($locationFilter)): ?>
                    <div class="active-filters mt-3">
                        <div class="active-filters-label">Active filters:</div>
                        <div class="active-filters-badges">
                            <?php if (!empty($categoryFilter)):
                                // Get category name
                                mysqli_data_seek($categoriesResult, 0);
                                $categoryName = "";
                                while ($category = mysqli_fetch_assoc($categoriesResult)) {
                                    if ($category['category_id'] == $categoryFilter) {
                                        $categoryName = $category['category_name'];
                                        break;
                                    }
                                }
                            ?>
                            <div class="filter-badge category">
                                <span class="filter-badge-label">Category:</span>
                                <span class="filter-badge-value"><?php echo $categoryName; ?></span>
                                <a href="?type=<?php echo $itemType; ?><?php echo !empty($locationFilter) ? '&location=' . $locationFilter : ''; ?>" class="filter-badge-remove">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                            <?php endif; ?>

                            <?php if (!empty($locationFilter) && hasRole('GodMode')): ?>
                            <div class="filter-badge location">
                                <span class="filter-badge-label">Location:</span>
                                <span class="filter-badge-value"><?php echo $locations[$locationFilter]; ?></span>
                                <a href="?type=<?php echo $itemType; ?><?php echo !empty($categoryFilter) ? '&category=' . $categoryFilter : ''; ?>" class="filter-badge-remove">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endif; ?>
                </form>
            </div>

            <form id="batchTransferForm" action="process.php" method="post">
                <!-- Hidden inputs for mixed selections -->
                <input type="hidden" name="item_type" value="mixed">
                <div id="hiddenAssetInputs"></div>
                <div id="hiddenInventoryInputs"></div>

                <!-- Tab Content Areas -->
                <div class="tab-content">
                    <!-- Assets Tab -->
                    <div class="tab-pane <?php echo $itemType == 'asset' ? 'show active' : ''; ?>" id="assetsTabContent">
                        <div class="table-responsive animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.2s;">
                            <table class="table table-hover" id="assetsTable" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <label class="custom-checkbox">
                                                <input type="checkbox" id="selectAllAssets">
                                                <span class="checkmark"></span>
                                            </label>
                                        </th>
                                        <th>Asset ID</th>
                                        <th>SKU Code</th>
                                        <th>Item Name</th>
                                        <th>Serial Number</th>
                                        <th>Category</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($itemType == 'asset' && mysqli_num_rows($result) > 0): ?>
                                        <?php while ($asset = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td>
                                                    <label class="custom-checkbox">
                                                        <input type="checkbox" name="selected_assets[]" value="<?php echo $asset['asset_id']; ?>" class="asset-checkbox" data-type="asset">
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </td>
                                                <td><?php echo $asset['asset_id']; ?></td>
                                                <td><?php echo $asset['sku_code']; ?></td>
                                                <td class="fw-medium"><?php echo $asset['asset_name']; ?></td>
                                                <td><?php echo $asset['serial_number']; ?></td>
                                                <td>
                                                    <span class="badge bg-primary bg-opacity-10 text-primary rounded-pill">
                                                        <?php echo $asset['category_name']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary bg-opacity-10 text-secondary rounded-pill">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?php echo $asset['location_name']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success bg-opacity-10 text-success rounded-pill">
                                                        <?php echo $asset['status']; ?>
                                                    </span>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="8" class="text-center py-5">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">No assets found</h5>
                                                    <p class="text-muted">Try adjusting your filter criteria or switch to the Consumable Inventory tab</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Inventory Tab -->
                    <div class="tab-pane <?php echo $itemType == 'inventory' ? 'show active' : ''; ?>" id="inventoryTabContent">
                        <div class="table-responsive animate__animated animate__fadeInUp animate__faster" style="animation-delay: 0.2s;">
                            <table class="table table-hover" id="inventoryTable" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">
                                            <label class="custom-checkbox">
                                                <input type="checkbox" id="selectAllInventory">
                                                <span class="checkmark"></span>
                                            </label>
                                        </th>
                                        <th>Inventory ID</th>
                                        <th>SKU Code</th>
                                        <th>Item Name</th>
                                        <th>Category</th>
                                        <th>Current Quantity (with Unit)</th>
                                        <th>Location</th>
                                        <th>Status</th>
                                        <th>Quantity to Transfer (with Unit)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if ($itemType == 'inventory' && mysqli_num_rows($result) > 0): ?>
                                        <?php while ($inventory = mysqli_fetch_assoc($result)): ?>
                                            <tr>
                                                <td>
                                                    <label class="custom-checkbox">
                                                        <?php $isPreselected = in_array($inventory['inventory_id'], $preselectedInventory); ?>
                                                        <input type="checkbox" name="selected_inventory[]" value="<?php echo $inventory['inventory_id']; ?>" class="inventory-checkbox" data-type="inventory" <?php echo $isPreselected ? 'checked' : ''; ?>>
                                                        <span class="checkmark"></span>
                                                    </label>
                                                </td>
                                                <td><?php echo $inventory['inventory_id']; ?></td>
                                                <td><?php echo $inventory['sku_code']; ?></td>
                                                <td class="fw-medium"><?php echo $inventory['sku_name']; ?></td>
                                                <td>
                                                    <span class="badge bg-primary bg-opacity-10 text-primary rounded-pill">
                                                        <?php echo $inventory['category_name']; ?>
                                                    </span>
                                                </td>
                                                <td class="fw-medium">
                                                    <?php echo $inventory['current_quantity']; ?>
                                                    <span class="badge bg-light text-dark rounded-pill ms-1"><?php echo $inventory['unit_of_measure']; ?></span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-secondary bg-opacity-10 text-secondary rounded-pill">
                                                        <i class="fas fa-map-marker-alt me-1"></i>
                                                        <?php echo $inventory['location_name']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <span class="badge bg-success bg-opacity-10 text-success rounded-pill">
                                                        <?php echo $inventory['status']; ?>
                                                    </span>
                                                </td>
                                                <td>
                                                    <div class="input-group input-group-sm">
                                                        <span class="input-group-text bg-light border-end-0"><i class="fas fa-hashtag text-primary"></i></span>
                                                        <input type="number" name="transfer_quantity[<?php echo $inventory['inventory_id']; ?>]" class="form-control border-start-0 border-end-0 quantity-input" min="1" max="<?php echo $inventory['current_quantity']; ?>" value="1" required <?php echo $isPreselected ? '' : 'disabled'; ?> data-inventory-id="<?php echo $inventory['inventory_id']; ?>">
                                                        <span class="input-group-text bg-light border-start-0"><?php echo $inventory['unit_of_measure']; ?></span>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endwhile; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="9" class="text-center py-5">
                                                <div class="d-flex flex-column align-items-center">
                                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                                    <h5 class="text-muted">No inventory items found</h5>
                                                    <p class="text-muted">Try adjusting your filter criteria or switch to the Fixed Assets tab</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <div class="mt-5 text-center">
                    <button type="submit" class="next-button" id="nextButton" disabled>
                        <i class="fas fa-arrow-right me-2"></i> Next - Select Destination
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Session storage keys
    const STORAGE_KEYS = {
        SELECTED_ASSETS: 'batchTransfer_selectedAssets',
        SELECTED_INVENTORY: 'batchTransfer_selectedInventory',
        INVENTORY_QUANTITIES: 'batchTransfer_inventoryQuantities'
    };

    // Show loading animation
    document.querySelectorAll('.animate__animated').forEach(function(element, index) {
        element.style.opacity = '0';
        setTimeout(function() {
            element.style.opacity = '1';
        }, 100 * index);
    });

    // Initialize DataTables with modern styling
    const table = $('#assetsTable, #inventoryTable').DataTable({
        pageLength: 25,
        columnDefs: [
            { orderable: false, targets: 0 }
        ],
        "initComplete": function() {
            // Add custom styling to the DataTables elements
            $('.dataTables_wrapper .dataTables_filter input').addClass('form-control form-control-sm ms-2').css('width', '200px');
            $('.dataTables_wrapper .dataTables_length select').addClass('form-select form-select-sm');
            $('.dataTables_info').addClass('text-muted small pt-3');
        }
    });

    // Tab switching functionality
    document.querySelectorAll('[data-tab]').forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            const targetTab = this.getAttribute('data-tab');

            // Save current selections before switching
            saveCurrentSelections();

            // Switch to the target tab
            switchToTab(targetTab);
        });
    });

    // Function to switch tabs
    function switchToTab(tabType) {
        // Update URL without page reload
        const url = new URL(window.location);
        url.searchParams.set('type', tabType);
        window.history.pushState({}, '', url);

        // Update tab appearance
        document.querySelectorAll('.nav-link').forEach(link => {
            link.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabType}"]`).classList.add('active');

        // Update tab content
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('show', 'active');
        });

        if (tabType === 'asset') {
            document.getElementById('assetsTabContent').classList.add('show', 'active');
        } else {
            document.getElementById('inventoryTabContent').classList.add('show', 'active');
        }

        // Load data for the new tab if needed
        if ((tabType === 'asset' && !document.querySelector('.asset-checkbox')) ||
            (tabType === 'inventory' && !document.querySelector('.inventory-checkbox'))) {
            // Reload page to fetch data for the new tab
            window.location.href = url.toString();
        } else {
            // Restore selections for the current tab
            restoreSelections();
            updateTabCounts();
            updateNextButtonState();
        }
    }

    // Function to save current selections to session storage
    function saveCurrentSelections() {
        // Save asset selections
        const selectedAssets = [];
        document.querySelectorAll('.asset-checkbox:checked').forEach(checkbox => {
            selectedAssets.push(checkbox.value);
        });
        sessionStorage.setItem(STORAGE_KEYS.SELECTED_ASSETS, JSON.stringify(selectedAssets));

        // Save inventory selections and quantities
        const selectedInventory = [];
        const inventoryQuantities = {};
        document.querySelectorAll('.inventory-checkbox:checked').forEach(checkbox => {
            const inventoryId = checkbox.value;
            selectedInventory.push(inventoryId);

            const quantityInput = document.querySelector(`input[data-inventory-id="${inventoryId}"]`);
            if (quantityInput) {
                inventoryQuantities[inventoryId] = quantityInput.value;
            }
        });
        sessionStorage.setItem(STORAGE_KEYS.SELECTED_INVENTORY, JSON.stringify(selectedInventory));
        sessionStorage.setItem(STORAGE_KEYS.INVENTORY_QUANTITIES, JSON.stringify(inventoryQuantities));
    }

    // Function to restore selections from session storage
    function restoreSelections() {
        // Restore asset selections
        const savedAssets = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_ASSETS) || '[]');
        savedAssets.forEach(assetId => {
            const checkbox = document.querySelector(`.asset-checkbox[value="${assetId}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });

        // Restore inventory selections and quantities
        const savedInventory = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_INVENTORY) || '[]');
        const savedQuantities = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.INVENTORY_QUANTITIES) || '{}');

        savedInventory.forEach(inventoryId => {
            const checkbox = document.querySelector(`.inventory-checkbox[value="${inventoryId}"]`);
            if (checkbox) {
                checkbox.checked = true;
                updateQuantityFields(checkbox);

                // Restore quantity
                const quantityInput = document.querySelector(`input[data-inventory-id="${inventoryId}"]`);
                if (quantityInput && savedQuantities[inventoryId]) {
                    quantityInput.value = savedQuantities[inventoryId];
                }
            }
        });
    }

    // Function to update tab counts
    function updateTabCounts() {
        const assetCount = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_ASSETS) || '[]').length;
        const inventoryCount = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_INVENTORY) || '[]').length;

        const assetBadge = document.getElementById('assetCount');
        const inventoryBadge = document.getElementById('inventoryCount');

        if (assetCount > 0) {
            assetBadge.textContent = assetCount;
            assetBadge.style.display = 'inline';
        } else {
            assetBadge.style.display = 'none';
        }

        if (inventoryCount > 0) {
            inventoryBadge.textContent = inventoryCount;
            inventoryBadge.style.display = 'inline';
        } else {
            inventoryBadge.style.display = 'none';
        }
    }

    // Select All functionality for assets
    document.getElementById('selectAllAssets')?.addEventListener('change', function() {
        const isChecked = this.checked;
        document.querySelectorAll('.asset-checkbox').forEach(checkbox => {
            checkbox.checked = isChecked;
        });
        saveCurrentSelections();
        updateTabCounts();
        updateNextButtonState();
    });

    // Select All functionality for inventory
    document.getElementById('selectAllInventory')?.addEventListener('change', function() {
        const isChecked = this.checked;
        document.querySelectorAll('.inventory-checkbox').forEach(checkbox => {
            checkbox.checked = isChecked;
            updateQuantityFields(checkbox);
        });
        saveCurrentSelections();
        updateTabCounts();
        updateNextButtonState();
    });

    // Individual checkbox change for assets
    document.querySelectorAll('.asset-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            saveCurrentSelections();
            updateTabCounts();
            updateNextButtonState();
        });
    });

    // Individual checkbox change for inventory
    document.querySelectorAll('.inventory-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateQuantityFields(this);
            saveCurrentSelections();
            updateTabCounts();
            updateNextButtonState();
        });
    });

    // Quantity input change for inventory
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.addEventListener('change', function() {
            saveCurrentSelections();
        });
    });

    // Function to enable/disable the quantity input fields
    function updateQuantityFields(checkbox) {
        const row = checkbox.closest('tr');
        const quantityInput = row.querySelector('input[type="number"]');

        if (quantityInput) {
            quantityInput.disabled = !checkbox.checked;
            if (checkbox.checked) {
                quantityInput.parentElement.classList.add('animate__animated', 'animate__fadeIn');
                quantityInput.focus();
            }
        }
    }

    // Function to enable/disable the next button and update hidden inputs
    function updateNextButtonState() {
        const assetCount = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_ASSETS) || '[]').length;
        const inventoryCount = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_INVENTORY) || '[]').length;
        const totalCount = assetCount + inventoryCount;

        const nextButton = document.getElementById('nextButton');
        nextButton.disabled = totalCount === 0;

        if (totalCount > 0) {
            nextButton.innerHTML = `<i class="fas fa-arrow-right me-2"></i> Next - Select Destination (${totalCount} items)`;
            nextButton.classList.add('animate__animated', 'animate__pulse');
            setTimeout(() => {
                nextButton.classList.remove('animate__animated', 'animate__pulse');
            }, 1000);
        } else {
            nextButton.innerHTML = `<i class="fas fa-arrow-right me-2"></i> Next - Select Destination`;
        }

        // Update hidden form inputs for submission
        updateHiddenInputs();
    }

    // Function to update hidden form inputs
    function updateHiddenInputs() {
        const assetInputsContainer = document.getElementById('hiddenAssetInputs');
        const inventoryInputsContainer = document.getElementById('hiddenInventoryInputs');

        // Clear existing inputs
        assetInputsContainer.innerHTML = '';
        inventoryInputsContainer.innerHTML = '';

        // Add asset inputs
        const selectedAssets = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_ASSETS) || '[]');
        selectedAssets.forEach(assetId => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_assets[]';
            input.value = assetId;
            assetInputsContainer.appendChild(input);
        });

        // Add inventory inputs
        const selectedInventory = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.SELECTED_INVENTORY) || '[]');
        const savedQuantities = JSON.parse(sessionStorage.getItem(STORAGE_KEYS.INVENTORY_QUANTITIES) || '{}');

        selectedInventory.forEach(inventoryId => {
            // Add inventory selection input
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_inventory[]';
            input.value = inventoryId;
            inventoryInputsContainer.appendChild(input);

            // Add quantity input
            const quantityInput = document.createElement('input');
            quantityInput.type = 'hidden';
            quantityInput.name = `transfer_quantity[${inventoryId}]`;
            quantityInput.value = savedQuantities[inventoryId] || '1';
            inventoryInputsContainer.appendChild(quantityInput);
        });
    }

    // Initialize the page
    function initializePage() {
        // Restore selections from session storage
        restoreSelections();

        // Update tab counts
        updateTabCounts();

        // Update next button state
        updateNextButtonState();
    }

    // Initialize page on load
    initializePage();

    // Clear selections when starting fresh (optional - you can remove this if you want persistent selections across page refreshes)
    // Uncomment the next line if you want to clear selections when the page loads fresh (not from tab switching)
    // if (!document.referrer.includes('select.php')) { sessionStorage.clear(); }

    // Handle filter form submission
    document.getElementById('filterForm')?.addEventListener('submit', function(e) {
        // Save selections before filtering
        saveCurrentSelections();
        showLoading();
    });

    // Handle category filter change for instant filtering
    document.getElementById('filterCategory')?.addEventListener('change', function() {
        if (this.value !== '') {
            saveCurrentSelections();
            showLoading();
            document.getElementById('filterForm').submit();
        }
    });

    // Handle location filter change for instant filtering (if exists)
    document.getElementById('filterLocation')?.addEventListener('change', function() {
        if (this.value !== '') {
            saveCurrentSelections();
            showLoading();
            document.getElementById('filterForm').submit();
        }
    });

    // Show loading indicator
    function showLoading() {
        if (!document.getElementById('loadingOverlay')) {
            const overlay = document.createElement('div');
            overlay.id = 'loadingOverlay';
            overlay.style.position = 'fixed';
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(255,255,255,0.7)';
            overlay.style.zIndex = '9999';
            overlay.style.display = 'flex';
            overlay.style.justifyContent = 'center';
            overlay.style.alignItems = 'center';

            const content = document.createElement('div');
            content.style.backgroundColor = 'white';
            content.style.padding = '20px';
            content.style.borderRadius = '10px';
            content.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
            content.style.textAlign = 'center';

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border text-primary mb-3';
            spinner.setAttribute('role', 'status');

            const spinnerText = document.createElement('span');
            spinnerText.className = 'visually-hidden';
            spinnerText.textContent = 'Loading...';

            const text = document.createElement('div');
            text.textContent = 'Loading items...';

            spinner.appendChild(spinnerText);
            content.appendChild(spinner);
            content.appendChild(text);
            overlay.appendChild(content);
            document.body.appendChild(overlay);
        } else {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
    }

    // Add hover effect to table rows
    document.querySelectorAll('tbody tr').forEach(row => {
        row.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(76, 175, 80, 0.05)';
            this.style.transition = 'background-color 0.2s ease';
        });

        row.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });
    });
});
</script>

<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/choims/includes/footer.php');
?>